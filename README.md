# Streamy - تطبيق مشاركة الفيديوهات

تطبيق مشابه للتيك توك مع لوحة تحكم إدارية مطور بـ Flutter و PHP.

## المميزات

### تطبيق Flutter (Android):
- 🔐 تسجيل الدخول والتسجيل باستخدام Firebase Authentication
- 📱 واجهة مستخدم مشابهة للتيك توك
- 🎥 رفع ومشاهدة الفيديوهات
- ❤️ نظام الإعجابات والتعليقات
- 👥 متابعة المستخدمين
- 🔍 البحث في الفيديوهات والمستخدمين
- 📊 إحصائيات المستخدم
- 🎵 دعم الموسيقى في الفيديوهات

### لوحة التحكم (PHP):
- 📊 لوحة تحكم شاملة مع الإحصائيات
- 👥 إدارة المستخدمين (تفعيل/إلغاء تفعيل/توثيق)
- 🎥 إدارة الفيديوهات (موافقة/رفض/حذف)
- 💬 إدارة التعليقات
- 🚩 نظام البلاغات
- 📈 تحليلات مفصلة
- ⚙️ إعدادات التطبيق
- 🔒 نظام صلاحيات متعدد المستويات

## التقنيات المستخدمة

### Frontend (Flutter):
- Flutter SDK
- Firebase (Auth, Firestore, Storage)
- Provider (State Management)
- Video Player
- Image Picker
- Cached Network Image

### Backend (PHP):
- PHP 7.4+
- MySQL
- Bootstrap 5
- Chart.js
- Font Awesome

### قاعدة البيانات:
- Firebase Firestore (للبيانات الفورية)
- MySQL (للوحة التحكم والإحصائيات)

## متطلبات التشغيل

### للتطبيق:
- Flutter SDK 3.8.1+
- Android Studio / VS Code
- حساب Firebase مع إعداد المشروع

### للوحة التحكم:
- PHP 7.4+
- MySQL 5.7+
- Apache/Nginx
- Composer (اختياري)

## طريقة التشغيل

### 1. إعداد Firebase:
```bash
# إنشاء مشروع Firebase جديد
# تفعيل Authentication, Firestore, Storage
# تحميل ملف google-services.json ووضعه في android/app/
```

### 2. تشغيل تطبيق Flutter:
```bash
# تثبيت المكتبات
flutter pub get

# تشغيل التطبيق
flutter run
```

### 3. إعداد لوحة التحكم:
```bash
# إنشاء قاعدة بيانات MySQL
CREATE DATABASE streamy_db;

# رفع ملفات لوحة التحكم إلى الخادم
# تعديل إعدادات قاعدة البيانات في config/database.php

# الوصول إلى لوحة التحكم
http://localhost/admin_panel/

# بيانات الدخول الافتراضية:
# اسم المستخدم: admin
# كلمة المرور: admin123
```

## هيكل المشروع

```
streamy/
├── lib/                          # كود Flutter
│   ├── models/                   # نماذج البيانات
│   ├── providers/                # مقدمي الخدمة (State Management)
│   ├── screens/                  # شاشات التطبيق
│   ├── widgets/                  # الويدجت المخصصة
│   ├── utils/                    # الأدوات المساعدة
│   └── main.dart                 # نقطة البداية
├── android/                      # إعدادات Android
├── admin_panel/                  # لوحة التحكم PHP
│   ├── config/                   # إعدادات قاعدة البيانات
│   ├── includes/                 # الملفات المشتركة
│   ├── assets/                   # الملفات الثابتة (CSS, JS, Images)
│   └── *.php                     # صفحات لوحة التحكم
└── README.md
```

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. عمل commit للتغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في GitHub.

## ملاحظات مهمة

1. تأكد من إعداد Firebase بشكل صحيح
2. قم بتحديث إعدادات قاعدة البيانات في لوحة التحكم
3. تأكد من صلاحيات الكتابة في مجلد uploads
4. استخدم HTTPS في الإنتاج لضمان الأمان

## التطوير المستقبلي

- [ ] إضافة ميزة البث المباشر
- [ ] تطبيق iOS
- [ ] إشعارات push
- [ ] نظام الرسائل الخاصة
- [ ] فلاتر الفيديو والتأثيرات
- [ ] نظام النقاط والمكافآت