# 🚨 حل عاجل للمشاكل - Streamy

## 🖥️ مشكلة لوحة التحكم - حل فوري

### الخطوة 1: إنشاء قاعدة البيانات والمستخدم
```bash
# افتح المتصفح واذهب إلى:
http://localhost/admin_panel/create_admin.php
```

### الخطوة 2: إذا لم تعمل الخطوة الأولى
افتح phpMyAdmin أو MySQL Command Line وشغل هذا الكود:

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS streamy_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE streamy_db;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول المستخدمين الإداريين
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- حذف المستخدم الموجود (إن وجد)
DELETE FROM admin_users WHERE username = 'admin';

-- إنشاء المستخدم الإداري
INSERT INTO admin_users (username, email, password_hash, full_name, role, is_active) 
VALUES ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'super_admin', 1);
```

### الخطوة 3: تسجيل الدخول
```
الرابط: http://localhost/admin_panel/login.php
اسم المستخدم: admin
كلمة المرور: admin123
```

---

## 🎥 مشكلة رفع الفيديو - حل فوري

### المشكلة الأساسية:
Firebase Storage غير مُعد بشكل صحيح

### الحل السريع:

#### 1. اذهب إلى Firebase Console:
```
https://console.firebase.google.com/
```

#### 2. اختر مشروعك أو أنشئ مشروع جديد

#### 3. فعّل Storage:
- اذهب إلى Storage في القائمة الجانبية
- انقر "Get started"
- اختر "Start in test mode"
- اختر الموقع الجغرافي

#### 4. غيّر قواعد Storage (مؤقتاً للتطوير):
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if true;
    }
  }
}
```

#### 5. تأكد من ملف google-services.json:
- يجب أن يكون في `android/app/google-services.json`
- يحتوي على بيانات صحيحة من مشروعك

#### 6. فعّل Authentication:
- اذهب إلى Authentication
- فعّل Email/Password

#### 7. فعّل Firestore:
- اذهب إلى Firestore Database
- أنشئ قاعدة بيانات في test mode

---

## 🔥 إعداد Firebase سريع (5 دقائق)

### 1. إنشاء مشروع:
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر "Add project"
3. اسم المشروع: `Streamy`
4. فعّل Google Analytics (اختياري)

### 2. إضافة تطبيق Android:
1. انقر أيقونة Android
2. Package name: `com.example.streamy`
3. App nickname: `Streamy`
4. تحميل `google-services.json`
5. ضعه في `android/app/`

### 3. تفعيل الخدمات:
```
✅ Authentication → Email/Password
✅ Firestore Database → Test mode
✅ Storage → Test mode
```

### 4. قواعد Firestore (للتطوير):
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

### 5. قواعد Storage (للتطوير):
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if true;
    }
  }
}
```

---

## 🛠️ اختبار سريع

### 1. اختبار لوحة التحكم:
```bash
# افتح:
http://localhost/admin_panel/create_admin.php

# ثم:
http://localhost/admin_panel/login.php
# admin / admin123
```

### 2. اختبار التطبيق:
```bash
flutter clean
flutter pub get
flutter run
```

### 3. اختبار رفع الفيديو:
1. سجل حساب جديد في التطبيق
2. اذهب لتبويب "رفع"
3. اختر فيديو قصير (أقل من 10 ثانية للاختبار)
4. أضف وصف
5. انقر "نشر الفيديو"

---

## 🚨 إذا لم تعمل الحلول

### مشكلة لوحة التحكم:
```bash
# تحقق من MySQL:
mysql -u root -p

# في MySQL:
SHOW DATABASES;
USE streamy_db;
SHOW TABLES;
SELECT * FROM admin_users;
```

### مشكلة Firebase:
```bash
# تحقق من الملفات:
ls android/app/google-services.json

# تحقق من السجلات:
flutter logs
```

### مشكلة عامة:
```bash
# إعادة تشغيل كل شيء:
flutter clean
flutter pub get

# إعادة تشغيل الخوادم:
# أعد تشغيل XAMPP/WAMP
# أعد تشغيل Android Studio
```

---

## 📞 دعم سريع

إذا لم تعمل أي من الحلول:

1. **تأكد من تشغيل XAMPP/WAMP**
2. **تأكد من وجود مشروع Firebase**
3. **تأكد من ملف google-services.json في المكان الصحيح**
4. **جرب إنشاء مشروع Firebase جديد**

---

🎯 **هذه الحلول يجب أن تصلح المشاكل في أقل من 10 دقائق!**
