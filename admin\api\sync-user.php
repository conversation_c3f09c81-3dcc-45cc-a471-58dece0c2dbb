<?php
// API لمزامنة بيانات المستخدمين من Firebase إلى قاعدة البيانات
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

$response = ['success' => false, 'message' => ''];

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $firebase_uid = $input['firebase_uid'] ?? '';
        $email = $input['email'] ?? '';
        $username = $input['username'] ?? '';
        $full_name = $input['full_name'] ?? '';
        $profile_image = $input['profile_image'] ?? '';
        $bio = $input['bio'] ?? '';
        
        if (empty($firebase_uid) || empty($email) || empty($username) || empty($full_name)) {
            throw new Exception('البيانات المطلوبة مفقودة');
        }
        
        // التحقق من وجود المستخدم
        $stmt = $pdo->prepare("SELECT id FROM app_users WHERE firebase_uid = ? OR email = ? OR username = ?");
        $stmt->execute([$firebase_uid, $email, $username]);
        $existingUser = $stmt->fetch();
        
        if ($existingUser) {
            // تحديث المستخدم الموجود
            $stmt = $pdo->prepare("
                UPDATE app_users 
                SET email = ?, username = ?, full_name = ?, profile_image = ?, bio = ?, updated_at = NOW()
                WHERE firebase_uid = ?
            ");
            $stmt->execute([$email, $username, $full_name, $profile_image, $bio, $firebase_uid]);
            
            $response['success'] = true;
            $response['message'] = 'تم تحديث بيانات المستخدم';
            $response['user_id'] = $existingUser['id'];
        } else {
            // إنشاء مستخدم جديد
            $stmt = $pdo->prepare("
                INSERT INTO app_users (firebase_uid, email, username, full_name, profile_image, bio, created_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$firebase_uid, $email, $username, $full_name, $profile_image, $bio]);
            
            $response['success'] = true;
            $response['message'] = 'تم إنشاء المستخدم بنجاح';
            $response['user_id'] = $pdo->lastInsertId();
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // الحصول على بيانات المستخدم
        $firebase_uid = $_GET['firebase_uid'] ?? '';
        
        if (empty($firebase_uid)) {
            throw new Exception('معرف Firebase مطلوب');
        }
        
        $stmt = $pdo->prepare("
            SELECT id, firebase_uid, email, username, full_name, profile_image, bio,
                   followers_count, following_count, likes_count, videos_count,
                   is_verified, is_active, created_at, updated_at
            FROM app_users 
            WHERE firebase_uid = ?
        ");
        $stmt->execute([$firebase_uid]);
        $user = $stmt->fetch();
        
        if ($user) {
            $response['success'] = true;
            $response['user'] = $user;
        } else {
            throw new Exception('المستخدم غير موجود');
        }
    }
    
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = $e->getMessage();
    error_log("API Error: " . $e->getMessage());
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
