<?php
// API لمزامنة بيانات الفيديوهات من Firebase إلى قاعدة البيانات
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

$response = ['success' => false, 'message' => ''];

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $firebase_id = $input['firebase_id'] ?? '';
        $firebase_uid = $input['firebase_uid'] ?? '';
        $video_url = $input['video_url'] ?? '';
        $thumbnail_url = $input['thumbnail_url'] ?? '';
        $caption = $input['caption'] ?? '';
        $hashtags = $input['hashtags'] ?? [];
        $music_name = $input['music_name'] ?? '';
        $music_url = $input['music_url'] ?? '';
        $duration = $input['duration'] ?? 0;
        $is_public = $input['is_public'] ?? true;
        
        if (empty($firebase_id) || empty($firebase_uid) || empty($video_url)) {
            throw new Exception('البيانات المطلوبة مفقودة');
        }
        
        // الحصول على معرف المستخدم من قاعدة البيانات
        $stmt = $pdo->prepare("SELECT id FROM app_users WHERE firebase_uid = ?");
        $stmt->execute([$firebase_uid]);
        $user = $stmt->fetch();
        
        if (!$user) {
            throw new Exception('المستخدم غير موجود في قاعدة البيانات');
        }
        
        $user_id = $user['id'];
        
        // التحقق من وجود الفيديو
        $stmt = $pdo->prepare("SELECT id FROM videos WHERE firebase_id = ?");
        $stmt->execute([$firebase_id]);
        $existingVideo = $stmt->fetch();
        
        if ($existingVideo) {
            // تحديث الفيديو الموجود
            $stmt = $pdo->prepare("
                UPDATE videos 
                SET video_url = ?, thumbnail_url = ?, caption = ?, hashtags = ?, 
                    music_name = ?, music_url = ?, duration = ?, is_public = ?, updated_at = NOW()
                WHERE firebase_id = ?
            ");
            $stmt->execute([
                $video_url, $thumbnail_url, $caption, json_encode($hashtags),
                $music_name, $music_url, $duration, $is_public, $firebase_id
            ]);
            
            $response['success'] = true;
            $response['message'] = 'تم تحديث بيانات الفيديو';
            $response['video_id'] = $existingVideo['id'];
        } else {
            // إنشاء فيديو جديد
            $stmt = $pdo->prepare("
                INSERT INTO videos (firebase_id, user_id, video_url, thumbnail_url, caption, hashtags,
                                  music_name, music_url, duration, is_public, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $firebase_id, $user_id, $video_url, $thumbnail_url, $caption, json_encode($hashtags),
                $music_name, $music_url, $duration, $is_public
            ]);
            
            $video_id = $pdo->lastInsertId();
            
            // تحديث عدد الفيديوهات للمستخدم
            $stmt = $pdo->prepare("UPDATE app_users SET videos_count = videos_count + 1 WHERE id = ?");
            $stmt->execute([$user_id]);
            
            $response['success'] = true;
            $response['message'] = 'تم إنشاء الفيديو بنجاح';
            $response['video_id'] = $video_id;
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // الحصول على بيانات الفيديو
        $firebase_id = $_GET['firebase_id'] ?? '';
        
        if (empty($firebase_id)) {
            throw new Exception('معرف Firebase مطلوب');
        }
        
        $stmt = $pdo->prepare("
            SELECT v.*, u.username, u.full_name, u.profile_image
            FROM videos v
            JOIN app_users u ON v.user_id = u.id
            WHERE v.firebase_id = ?
        ");
        $stmt->execute([$firebase_id]);
        $video = $stmt->fetch();
        
        if ($video) {
            // تحويل hashtags من JSON إلى array
            $video['hashtags'] = json_decode($video['hashtags'], true) ?: [];
            
            $response['success'] = true;
            $response['video'] = $video;
        } else {
            throw new Exception('الفيديو غير موجود');
        }
    }
    
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = $e->getMessage();
    error_log("API Error: " . $e->getMessage());
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
