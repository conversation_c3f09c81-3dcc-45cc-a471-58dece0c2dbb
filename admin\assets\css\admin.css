/* Streamy Admin Panel Styles */

:root {
    --primary-color: #FF0050;
    --primary-dark: #E6004A;
    --secondary-color: #FF4081;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 0.875rem;
    background-color: #f8f9fa;
    line-height: 1.5;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: #495057;
}

.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

/* Cards */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

/* Statistics Cards */
.border-start-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}

.border-start-success {
    border-left: 0.25rem solid var(--success-color) !important;
}

.border-start-info {
    border-left: 0.25rem solid var(--info-color) !important;
}

.border-start-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}

.text-xs {
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1rem 0.75rem;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
    padding: 0.75rem;
    border-top: 1px solid #e3e6f0;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.btn-sm {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.btn-lg {
    font-size: 1.125rem;
    padding: 0.75rem 1.5rem;
}

/* Forms */
.form-control {
    border-radius: 0.375rem;
    border: 1px solid var(--border-color);
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 0, 80, 0.25);
}

.form-select {
    border-radius: 0.375rem;
    border: 1px solid var(--border-color);
}

.input-group-text {
    background-color: #f8f9fa;
    border: 1px solid var(--border-color);
}

/* Alerts */
.alert {
    border-radius: 0.5rem;
    border: none;
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* Badges */
.badge {
    font-weight: 500;
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

/* Pagination */
.pagination .page-link {
    border-radius: 0.375rem;
    margin: 0 0.125rem;
    border: 1px solid var(--border-color);
    color: #5a5c69;
    padding: 0.5rem 0.75rem;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-link:hover {
    background-color: #f8f9fa;
    border-color: var(--border-color);
    color: #5a5c69;
}

/* Dropdowns */
.dropdown-menu {
    border-radius: 0.5rem;
    border: none;
    box-shadow: var(--shadow-lg);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.dropdown-header {
    font-weight: 600;
    color: #6c757d;
    padding: 0.5rem 1rem;
}

/* Avatar */
.avatar img {
    object-fit: cover;
    border-radius: 50%;
}

.avatar-sm {
    width: 2rem;
    height: 2rem;
}

.avatar-md {
    width: 3rem;
    height: 3rem;
}

.avatar-lg {
    width: 4rem;
    height: 4rem;
}

/* Progress */
.progress {
    height: 0.5rem;
    border-radius: 0.375rem;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 0.375rem;
}

/* Modals */
.modal-content {
    border-radius: 0.75rem;
    border: none;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Responsive */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.8rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .h2 {
        font-size: 1.5rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --card-bg: #2d2d2d;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Utility Classes */
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.border-radius-lg {
    border-radius: 0.75rem !important;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.font-weight-normal {
    font-weight: 400 !important;
}

.text-decoration-none {
    text-decoration: none !important;
}

/* Print styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .dropdown {
        display: none !important;
    }
    
    .main {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}
