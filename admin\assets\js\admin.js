// Streamy Admin Panel JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-refresh functionality
    initAutoRefresh();
    
    // Form validation
    initFormValidation();
    
    // Bulk actions
    initBulkActions();
    
    // Search functionality
    initSearch();
    
    // Charts initialization
    initCharts();
    
    // Real-time notifications
    initNotifications();
});

// Auto-refresh functionality
function initAutoRefresh() {
    const refreshInterval = 30000; // 30 seconds
    
    setInterval(function() {
        updateDashboardStats();
        updateNotificationCount();
    }, refreshInterval);
}

// Update dashboard statistics
function updateDashboardStats() {
    fetch('api/dashboard-stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatCard('total-users', data.stats.total_users);
                updateStatCard('total-videos', data.stats.total_videos);
                updateStatCard('total-views', data.stats.total_views);
                updateStatCard('total-likes', data.stats.total_likes);
            }
        })
        .catch(error => console.error('Error updating stats:', error));
}

// Update stat card
function updateStatCard(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = formatNumber(value);
        element.classList.add('updated');
        setTimeout(() => element.classList.remove('updated'), 1000);
    }
}

// Format numbers
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toLocaleString();
}

// Update notification count
function updateNotificationCount() {
    fetch('api/notification-count.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const badge = document.querySelector('.notification-badge');
                if (badge) {
                    if (data.count > 0) {
                        badge.textContent = data.count > 99 ? '99+' : data.count;
                        badge.style.display = 'inline';
                    } else {
                        badge.style.display = 'none';
                    }
                }
            }
        })
        .catch(error => console.error('Error updating notifications:', error));
}

// Form validation
function initFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
}

// Bulk actions
function initBulkActions() {
    const selectAllCheckbox = document.getElementById('select-all');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    const bulkActionBtn = document.getElementById('bulk-action-btn');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActionButton();
        });
    }
    
    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionButton);
    });
    
    function updateBulkActionButton() {
        const checkedItems = document.querySelectorAll('.item-checkbox:checked');
        if (bulkActionBtn) {
            bulkActionBtn.style.display = checkedItems.length > 0 ? 'block' : 'none';
            bulkActionBtn.textContent = `إجراءات مجمعة (${checkedItems.length})`;
        }
    }
}

// Search functionality
function initSearch() {
    const searchInput = document.getElementById('search-input');
    const searchForm = document.getElementById('search-form');
    
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 3 || this.value.length === 0) {
                    performSearch(this.value);
                }
            }, 500);
        });
    }
}

// Perform search
function performSearch(query) {
    const currentPage = window.location.pathname.split('/').pop();
    const searchParams = new URLSearchParams(window.location.search);
    
    if (query) {
        searchParams.set('search', query);
    } else {
        searchParams.delete('search');
    }
    
    searchParams.set('page', '1'); // Reset to first page
    
    const newUrl = currentPage + '?' + searchParams.toString();
    window.location.href = newUrl;
}

// Charts initialization
function initCharts() {
    // Users growth chart
    const usersChartCtx = document.getElementById('usersChart');
    if (usersChartCtx) {
        new Chart(usersChartCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'المستخدمون الجدد',
                    data: [12, 19, 3, 5, 2, 3],
                    borderColor: '#FF0050',
                    backgroundColor: 'rgba(255, 0, 80, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    // Videos distribution chart
    const videosChartCtx = document.getElementById('videosChart');
    if (videosChartCtx) {
        new Chart(videosChartCtx, {
            type: 'doughnut',
            data: {
                labels: ['معتمد', 'في انتظار الموافقة', 'مرفوض'],
                datasets: [{
                    data: [300, 50, 20],
                    backgroundColor: ['#28a745', '#ffc107', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

// Real-time notifications
function initNotifications() {
    // Check for new notifications every minute
    setInterval(checkNewNotifications, 60000);
}

function checkNewNotifications() {
    fetch('api/check-notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.hasNew) {
                showNotificationToast(data.message);
            }
        })
        .catch(error => console.error('Error checking notifications:', error));
}

// Show notification toast
function showNotificationToast(message) {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-primary border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-bell me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    const toastContainer = document.getElementById('toast-container') || createToastContainer();
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    const toast = new bootstrap.Toast(toastContainer.lastElementChild);
    toast.show();
}

// Create toast container
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

// Utility functions
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

function showLoading(element) {
    element.disabled = true;
    element.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري التحميل...';
}

function hideLoading(element, originalText) {
    element.disabled = false;
    element.innerHTML = originalText;
}

// AJAX form submission
function submitForm(formId, successCallback, errorCallback) {
    const form = document.getElementById(formId);
    const formData = new FormData(form);
    
    fetch(form.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (successCallback) successCallback(data);
        } else {
            if (errorCallback) errorCallback(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (errorCallback) errorCallback('حدث خطأ غير متوقع');
    });
}

// Export data
function exportData(type, format = 'csv') {
    const url = `api/export.php?type=${type}&format=${format}`;
    window.open(url, '_blank');
}

// Print page
function printPage() {
    window.print();
}

// Copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showNotificationToast('تم النسخ إلى الحافظة');
    });
}

// Smooth scroll to element
function scrollToElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
    }
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Format time ago
function timeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) return 'الآن';
    if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' دقيقة';
    if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' ساعة';
    if (diffInSeconds < 2592000) return Math.floor(diffInSeconds / 86400) + ' يوم';
    if (diffInSeconds < 31536000) return Math.floor(diffInSeconds / 2592000) + ' شهر';
    
    return Math.floor(diffInSeconds / 31536000) + ' سنة';
}

// Global error handler
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
    // You can send error reports to your logging service here
});

// Service worker registration (for PWA features)
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/admin/sw.js')
        .then(registration => console.log('SW registered'))
        .catch(error => console.log('SW registration failed'));
}
