<?php
// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'collectandwin2_hostmeed1';
$username = 'collectandwin2_hostmeed1';
$password = 'collectandwin2_hostmeed1';

try {
    // الاتصال بقاعدة البيانات مع إعدادات الترميز العربي
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // تعيين ترميز الاتصال
    $pdo->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("SET CHARACTER SET utf8mb4");
    $pdo->exec("SET character_set_connection=utf8mb4");
    
    // إنشاء الجداول المطلوبة
    $createTables = "
    -- جدول المستخدمين الإداريين
    CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );

    -- جدول المستخدمين
    CREATE TABLE IF NOT EXISTS app_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        firebase_uid VARCHAR(255) UNIQUE,
        email VARCHAR(255) UNIQUE NOT NULL,
        username VARCHAR(50) UNIQUE NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        profile_image VARCHAR(500),
        bio TEXT,
        followers_count INT DEFAULT 0,
        following_count INT DEFAULT 0,
        likes_count INT DEFAULT 0,
        videos_count INT DEFAULT 0,
        is_verified BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );

    -- جدول الفيديوهات
    CREATE TABLE IF NOT EXISTS videos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        firebase_id VARCHAR(255) UNIQUE,
        user_id INT,
        video_url VARCHAR(500) NOT NULL,
        thumbnail_url VARCHAR(500),
        caption TEXT,
        hashtags JSON,
        likes_count INT DEFAULT 0,
        comments_count INT DEFAULT 0,
        shares_count INT DEFAULT 0,
        views_count INT DEFAULT 0,
        duration DECIMAL(10,2) DEFAULT 0,
        is_public BOOLEAN DEFAULT TRUE,
        is_approved BOOLEAN DEFAULT TRUE,
        music_name VARCHAR(255),
        music_url VARCHAR(500),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES app_users(id) ON DELETE SET NULL
    );

    -- جدول التعليقات
    CREATE TABLE IF NOT EXISTS comments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        video_id INT,
        user_id INT,
        comment_text TEXT NOT NULL,
        likes_count INT DEFAULT 0,
        is_approved BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES app_users(id) ON DELETE CASCADE
    );

    -- جدول الإعجابات
    CREATE TABLE IF NOT EXISTS likes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        video_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_like (user_id, video_id),
        FOREIGN KEY (user_id) REFERENCES app_users(id) ON DELETE CASCADE,
        FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE
    );

    -- جدول المتابعات
    CREATE TABLE IF NOT EXISTS follows (
        id INT AUTO_INCREMENT PRIMARY KEY,
        follower_id INT,
        following_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_follow (follower_id, following_id),
        FOREIGN KEY (follower_id) REFERENCES app_users(id) ON DELETE CASCADE,
        FOREIGN KEY (following_id) REFERENCES app_users(id) ON DELETE CASCADE
    );

    -- جدول البلاغات
    CREATE TABLE IF NOT EXISTS reports (
        id INT AUTO_INCREMENT PRIMARY KEY,
        reporter_id INT,
        reported_user_id INT,
        reported_video_id INT,
        reported_comment_id INT,
        report_type ENUM('spam', 'inappropriate', 'harassment', 'copyright', 'other') NOT NULL,
        description TEXT,
        status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (reporter_id) REFERENCES app_users(id) ON DELETE CASCADE,
        FOREIGN KEY (reported_user_id) REFERENCES app_users(id) ON DELETE SET NULL,
        FOREIGN KEY (reported_video_id) REFERENCES videos(id) ON DELETE SET NULL,
        FOREIGN KEY (reported_comment_id) REFERENCES comments(id) ON DELETE SET NULL
    );

    -- جدول الإعدادات
    CREATE TABLE IF NOT EXISTS app_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        description TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );

    -- جدول الإحصائيات اليومية
    CREATE TABLE IF NOT EXISTS daily_stats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        date DATE UNIQUE NOT NULL,
        new_users INT DEFAULT 0,
        new_videos INT DEFAULT 0,
        total_views INT DEFAULT 0,
        total_likes INT DEFAULT 0,
        total_comments INT DEFAULT 0,
        total_shares INT DEFAULT 0,
        active_users INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    ";

    // تنفيذ إنشاء الجداول
    $pdo->exec($createTables);

    // إنشاء المستخدم الإداري الافتراضي
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM admin_users WHERE username = 'admin'");
    $stmt->execute();

    if ($stmt->fetchColumn() == 0) {
        $adminPassword = password_hash('Streamy@2024!', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO admin_users (username, email, password_hash, full_name, role, is_active)
            VALUES ('admin', '<EMAIL>', ?, 'System Administrator', 'super_admin', 1)
        ");
        $stmt->execute([$adminPassword]);
    }

    // إضافة الإعدادات الافتراضية
    $defaultSettings = [
        ['app_name', 'Streamy', 'اسم التطبيق'],
        ['app_version', '1.0.0', 'إصدار التطبيق'],
        ['max_video_duration', '180', 'أقصى مدة للفيديو بالثواني'],
        ['max_file_size', '100', 'أقصى حجم للملف بالميجابايت'],
        ['auto_approve_videos', '1', 'الموافقة التلقائية على الفيديوهات'],
        ['maintenance_mode', '0', 'وضع الصيانة'],
        ['registration_enabled', '1', 'تفعيل التسجيل الجديد'],
        ['email_verification', '0', 'تفعيل التحقق من البريد الإلكتروني'],
    ];
    
    foreach ($defaultSettings as $setting) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO app_settings (setting_key, setting_value, description) 
            VALUES (?, ?, ?)
        ");
        $stmt->execute($setting);
    }

} catch(PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}
?>
