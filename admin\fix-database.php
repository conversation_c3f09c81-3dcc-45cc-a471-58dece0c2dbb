<?php
// ملف إصلاح مشاكل قاعدة البيانات والترميز
$host = 'localhost';
$dbname = 'collectandwin2_hostmeed1';
$username = 'collectandwin2_hostmeed1';
$password = 'collectandwin2_hostmeed1';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح قاعدة البيانات - Streamy Admin</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem 0; }";
echo ".fix-container { max-width: 800px; margin: 0 auto; }";
echo ".card { box-shadow: 0 15px 35px rgba(0,0,0,0.1); border: none; border-radius: 15px; }";
echo ".card-header { background: linear-gradient(135deg, #FF0050 0%, #FF4081 100%); color: white; padding: 2rem; }";
echo ".step { margin: 1rem 0; padding: 1.5rem; border-radius: 10px; }";
echo ".step-success { background: #d4edda; border-left: 4px solid #28a745; }";
echo ".step-error { background: #f8d7da; border-left: 4px solid #dc3545; }";
echo ".step-warning { background: #fff3cd; border-left: 4px solid #ffc107; }";
echo ".step-info { background: #d1ecf1; border-left: 4px solid #17a2b8; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container fix-container'>";
echo "<div class='card'>";
echo "<div class='card-header text-center'>";
echo "<h1><i class='fas fa-tools me-3'></i>إصلاح قاعدة البيانات</h1>";
echo "<p class='mb-0'>حل مشاكل الترميز والنصوص العربية</p>";
echo "</div>";
echo "<div class='card-body'>";

$steps = [];
$hasError = false;

// الخطوة 1: الاتصال بقاعدة البيانات
echo "<div class='step step-info'>";
echo "<h4><i class='fas fa-database me-2'></i>الخطوة 1: الاتصال بقاعدة البيانات</h4>";

try {
    // الاتصال مع إعدادات الترميز الصحيحة
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // تعيين ترميز الاتصال
    $pdo->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("SET CHARACTER SET utf8mb4");
    $pdo->exec("SET character_set_connection=utf8mb4");
    
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم الاتصال بقاعدة البيانات بنجاح</p>";
    $steps[] = "✅ الاتصال بقاعدة البيانات";
} catch(PDOException $e) {
    echo "<p class='text-danger'><i class='fas fa-times me-2'></i>فشل الاتصال: " . $e->getMessage() . "</p>";
    $hasError = true;
    $steps[] = "❌ فشل الاتصال";
}
echo "</div>";

if (!$hasError) {
    // الخطوة 2: إصلاح ترميز قاعدة البيانات
    echo "<div class='step step-info'>";
    echo "<h4><i class='fas fa-font me-2'></i>الخطوة 2: إصلاح ترميز قاعدة البيانات</h4>";
    
    try {
        // تغيير ترميز قاعدة البيانات
        $pdo->exec("ALTER DATABASE $dbname CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم تحديث ترميز قاعدة البيانات</p>";
        $steps[] = "✅ ترميز قاعدة البيانات";
    } catch(Exception $e) {
        echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-2'></i>تحذير: " . $e->getMessage() . "</p>";
        $steps[] = "⚠️ تحذير في ترميز قاعدة البيانات";
    }
    echo "</div>";
    
    // الخطوة 3: حذف الجداول الموجودة وإعادة إنشائها
    echo "<div class='step step-info'>";
    echo "<h4><i class='fas fa-redo me-2'></i>الخطوة 3: إعادة إنشاء الجداول</h4>";
    
    try {
        // حذف الجداول الموجودة
        $tables = ['admin_users', 'app_users', 'videos', 'comments', 'likes', 'follows', 'reports', 'app_settings', 'daily_stats'];
        
        foreach ($tables as $table) {
            try {
                $pdo->exec("DROP TABLE IF EXISTS $table");
                echo "<p><i class='fas fa-trash text-warning me-2'></i>تم حذف جدول $table</p>";
            } catch(Exception $e) {
                // تجاهل الأخطاء
            }
        }
        
        // إنشاء الجداول من جديد
        $createTables = "
        -- جدول المستخدمين الإداريين
        CREATE TABLE admin_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
            is_active BOOLEAN DEFAULT TRUE,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

        -- جدول المستخدمين
        CREATE TABLE app_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            firebase_uid VARCHAR(255) UNIQUE,
            email VARCHAR(255) UNIQUE NOT NULL,
            username VARCHAR(50) UNIQUE NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            profile_image VARCHAR(500),
            bio TEXT,
            followers_count INT DEFAULT 0,
            following_count INT DEFAULT 0,
            likes_count INT DEFAULT 0,
            videos_count INT DEFAULT 0,
            is_verified BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

        -- جدول الفيديوهات
        CREATE TABLE videos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            firebase_id VARCHAR(255) UNIQUE,
            user_id INT,
            video_url VARCHAR(500) NOT NULL,
            thumbnail_url VARCHAR(500),
            caption TEXT,
            hashtags JSON,
            likes_count INT DEFAULT 0,
            comments_count INT DEFAULT 0,
            shares_count INT DEFAULT 0,
            views_count INT DEFAULT 0,
            duration DECIMAL(10,2) DEFAULT 0,
            is_public BOOLEAN DEFAULT TRUE,
            is_approved BOOLEAN DEFAULT TRUE,
            music_name VARCHAR(255),
            music_url VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES app_users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

        -- جدول الإعدادات
        CREATE TABLE app_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            description TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($createTables);
        echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم إنشاء الجداول بالترميز الصحيح</p>";
        $steps[] = "✅ إنشاء الجداول";
    } catch(Exception $e) {
        echo "<p class='text-danger'><i class='fas fa-times me-2'></i>خطأ في إنشاء الجداول: " . $e->getMessage() . "</p>";
        $hasError = true;
        $steps[] = "❌ خطأ في إنشاء الجداول";
    }
    echo "</div>";
}

if (!$hasError) {
    // الخطوة 4: إنشاء المستخدم الإداري
    echo "<div class='step step-info'>";
    echo "<h4><i class='fas fa-user-plus me-2'></i>الخطوة 4: إنشاء المستخدم الإداري</h4>";
    
    try {
        $adminPassword = password_hash('Streamy@2024!', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO admin_users (username, email, password_hash, full_name, role, is_active) 
            VALUES ('admin', '<EMAIL>', ?, 'System Administrator', 'super_admin', 1)
        ");
        $stmt->execute([$adminPassword]);
        
        echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم إنشاء المستخدم الإداري بنجاح</p>";
        $steps[] = "✅ المستخدم الإداري";
    } catch(Exception $e) {
        echo "<p class='text-danger'><i class='fas fa-times me-2'></i>خطأ في إنشاء المستخدم: " . $e->getMessage() . "</p>";
        $steps[] = "❌ خطأ في إنشاء المستخدم";
    }
    echo "</div>";
    
    // الخطوة 5: إضافة الإعدادات الافتراضية
    echo "<div class='step step-info'>";
    echo "<h4><i class='fas fa-cogs me-2'></i>الخطوة 5: إضافة الإعدادات</h4>";
    
    try {
        $defaultSettings = [
            ['app_name', 'Streamy', 'Application Name'],
            ['app_version', '1.0.0', 'Application Version'],
            ['max_video_duration', '180', 'Maximum video duration in seconds'],
            ['max_file_size', '100', 'Maximum file size in MB'],
            ['auto_approve_videos', '1', 'Auto approve videos'],
            ['maintenance_mode', '0', 'Maintenance mode'],
        ];
        
        foreach ($defaultSettings as $setting) {
            $stmt = $pdo->prepare("
                INSERT INTO app_settings (setting_key, setting_value, description) 
                VALUES (?, ?, ?)
            ");
            $stmt->execute($setting);
        }
        
        echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم إضافة الإعدادات الافتراضية</p>";
        $steps[] = "✅ الإعدادات الافتراضية";
    } catch(Exception $e) {
        echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-2'></i>تحذير في الإعدادات: " . $e->getMessage() . "</p>";
        $steps[] = "⚠️ تحذير في الإعدادات";
    }
    echo "</div>";
}

// ملخص النتائج
echo "<div class='step " . ($hasError ? 'step-error' : 'step-success') . "'>";
echo "<h4><i class='fas fa-clipboard-check me-2'></i>ملخص الإصلاح</h4>";
echo "<ul class='list-unstyled'>";
foreach ($steps as $step) {
    echo "<li class='mb-2'>$step</li>";
}
echo "</ul>";

if (!$hasError) {
    echo "<div class='alert alert-success mt-4'>";
    echo "<h5><i class='fas fa-check-circle me-2'></i>تم الإصلاح بنجاح!</h5>";
    echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
    echo "<ul>";
    echo "<li><strong>اسم المستخدم:</strong> admin</li>";
    echo "<li><strong>كلمة المرور:</strong> Streamy@2024!</li>";
    echo "</ul>";
    echo "<div class='text-center'>";
    echo "<a href='login.php' class='btn btn-success btn-lg me-3'>";
    echo "<i class='fas fa-sign-in-alt me-2'></i>تسجيل الدخول";
    echo "</a>";
    echo "<a href='setup.php' class='btn btn-info btn-lg'>";
    echo "<i class='fas fa-cog me-2'></i>اختبار الإعداد";
    echo "</a>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div class='alert alert-danger mt-4'>";
    echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>فشل في الإصلاح!</h5>";
    echo "<p>يرجى التحقق من صلاحيات قاعدة البيانات والمحاولة مرة أخرى.</p>";
    echo "<button onclick='location.reload()' class='btn btn-warning'>";
    echo "<i class='fas fa-redo me-2'></i>إعادة المحاولة";
    echo "</button>";
    echo "</div>";
}
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
