<?php
// دوال مساعدة للوحة التحكم

// التحقق من تسجيل الدخول
function checkLogin() {
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        header('Location: login.php');
        exit();
    }
}

// الحصول على إحصائيات لوحة التحكم
function getDashboardStats($pdo) {
    $stats = [];
    
    try {
        // إجمالي المستخدمين
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM app_users WHERE is_active = 1");
        $stats['total_users'] = $stmt->fetch()['count'];
        
        // إجمالي الفيديوهات
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE is_approved = 1");
        $stats['total_videos'] = $stmt->fetch()['count'];
        
        // إجمالي المشاهدات
        $stmt = $pdo->query("SELECT SUM(views_count) as total FROM videos");
        $stats['total_views'] = $stmt->fetch()['total'] ?: 0;
        
        // إجمالي الإعجابات
        $stmt = $pdo->query("SELECT SUM(likes_count) as total FROM videos");
        $stats['total_likes'] = $stmt->fetch()['total'] ?: 0;
        
        // المستخدمون الجدد اليوم
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM app_users WHERE DATE(created_at) = CURDATE()");
        $stats['new_users_today'] = $stmt->fetch()['count'];
        
        // الفيديوهات الجديدة اليوم
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE DATE(created_at) = CURDATE()");
        $stats['new_videos_today'] = $stmt->fetch()['count'];
        
        // البلاغات المعلقة
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM reports WHERE status = 'pending'");
        $stats['pending_reports'] = $stmt->fetch()['count'];
        
        // الفيديوهات في انتظار الموافقة
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE is_approved = 0");
        $stats['pending_videos'] = $stmt->fetch()['count'];
        
    } catch(PDOException $e) {
        error_log("Error getting dashboard stats: " . $e->getMessage());
    }
    
    return $stats;
}

// الحصول على المستخدمين الجدد
function getRecentUsers($pdo, $limit = 10) {
    try {
        $stmt = $pdo->prepare("
            SELECT id, username, full_name, email, profile_image, created_at, is_verified, is_active
            FROM app_users 
            ORDER BY created_at DESC 
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll();
    } catch(PDOException $e) {
        error_log("Error getting recent users: " . $e->getMessage());
        return [];
    }
}

// الحصول على الفيديوهات الجديدة
function getRecentVideos($pdo, $limit = 10) {
    try {
        $stmt = $pdo->prepare("
            SELECT v.id, v.caption, v.video_url, v.thumbnail_url, v.views_count, 
                   v.likes_count, v.created_at, v.is_approved,
                   u.username, u.full_name, u.profile_image
            FROM videos v
            LEFT JOIN app_users u ON v.user_id = u.id
            ORDER BY v.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll();
    } catch(PDOException $e) {
        error_log("Error getting recent videos: " . $e->getMessage());
        return [];
    }
}

// الحصول على جميع المستخدمين مع البحث والترقيم
function getUsers($pdo, $page = 1, $limit = 20, $search = '', $status = 'all') {
    $offset = ($page - 1) * $limit;
    
    $whereClause = "WHERE 1=1";
    $params = [];
    
    if (!empty($search)) {
        $whereClause .= " AND (username LIKE ? OR full_name LIKE ? OR email LIKE ?)";
        $searchTerm = "%$search%";
        $params = [$searchTerm, $searchTerm, $searchTerm];
    }
    
    if ($status !== 'all') {
        if ($status === 'active') {
            $whereClause .= " AND is_active = 1";
        } elseif ($status === 'inactive') {
            $whereClause .= " AND is_active = 0";
        } elseif ($status === 'verified') {
            $whereClause .= " AND is_verified = 1";
        }
    }
    
    try {
        // الحصول على العدد الإجمالي
        $countStmt = $pdo->prepare("SELECT COUNT(*) as total FROM app_users $whereClause");
        $countStmt->execute($params);
        $total = $countStmt->fetch()['total'];
        
        // الحصول على المستخدمين
        $stmt = $pdo->prepare("
            SELECT id, username, full_name, email, profile_image, 
                   followers_count, following_count, likes_count, videos_count,
                   is_verified, is_active, created_at
            FROM app_users 
            $whereClause
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        ");
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        $users = $stmt->fetchAll();
        
        return [
            'users' => $users,
            'total' => $total,
            'pages' => ceil($total / $limit),
            'current_page' => $page
        ];
    } catch(PDOException $e) {
        error_log("Error getting users: " . $e->getMessage());
        return ['users' => [], 'total' => 0, 'pages' => 0, 'current_page' => 1];
    }
}

// الحصول على جميع الفيديوهات مع البحث والترقيم
function getVideos($pdo, $page = 1, $limit = 20, $search = '', $status = 'all') {
    $offset = ($page - 1) * $limit;
    
    $whereClause = "WHERE 1=1";
    $params = [];
    
    if (!empty($search)) {
        $whereClause .= " AND (v.caption LIKE ? OR u.username LIKE ?)";
        $searchTerm = "%$search%";
        $params = [$searchTerm, $searchTerm];
    }
    
    if ($status !== 'all') {
        if ($status === 'approved') {
            $whereClause .= " AND v.is_approved = 1";
        } elseif ($status === 'pending') {
            $whereClause .= " AND v.is_approved = 0";
        } elseif ($status === 'public') {
            $whereClause .= " AND v.is_public = 1";
        } elseif ($status === 'private') {
            $whereClause .= " AND v.is_public = 0";
        }
    }
    
    try {
        // الحصول على العدد الإجمالي
        $countStmt = $pdo->prepare("
            SELECT COUNT(*) as total 
            FROM videos v 
            LEFT JOIN app_users u ON v.user_id = u.id 
            $whereClause
        ");
        $countStmt->execute($params);
        $total = $countStmt->fetch()['total'];
        
        // الحصول على الفيديوهات
        $stmt = $pdo->prepare("
            SELECT v.id, v.firebase_id, v.caption, v.video_url, v.thumbnail_url, 
                   v.likes_count, v.comments_count, v.views_count, v.shares_count,
                   v.is_public, v.is_approved, v.created_at,
                   u.username, u.full_name, u.profile_image
            FROM videos v
            LEFT JOIN app_users u ON v.user_id = u.id
            $whereClause
            ORDER BY v.created_at DESC
            LIMIT ? OFFSET ?
        ");
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        $videos = $stmt->fetchAll();
        
        return [
            'videos' => $videos,
            'total' => $total,
            'pages' => ceil($total / $limit),
            'current_page' => $page
        ];
    } catch(PDOException $e) {
        error_log("Error getting videos: " . $e->getMessage());
        return ['videos' => [], 'total' => 0, 'pages' => 0, 'current_page' => 1];
    }
}

// تحديث حالة المستخدم
function updateUserStatus($pdo, $userId, $status) {
    try {
        $stmt = $pdo->prepare("UPDATE app_users SET is_active = ? WHERE id = ?");
        return $stmt->execute([$status, $userId]);
    } catch(PDOException $e) {
        error_log("Error updating user status: " . $e->getMessage());
        return false;
    }
}

// تحديث حالة توثيق المستخدم
function updateUserVerification($pdo, $userId, $verified) {
    try {
        $stmt = $pdo->prepare("UPDATE app_users SET is_verified = ? WHERE id = ?");
        return $stmt->execute([$verified, $userId]);
    } catch(PDOException $e) {
        error_log("Error updating user verification: " . $e->getMessage());
        return false;
    }
}

// تحديث حالة موافقة الفيديو
function updateVideoApproval($pdo, $videoId, $approved) {
    try {
        $stmt = $pdo->prepare("UPDATE videos SET is_approved = ? WHERE id = ?");
        return $stmt->execute([$approved, $videoId]);
    } catch(PDOException $e) {
        error_log("Error updating video approval: " . $e->getMessage());
        return false;
    }
}

// حذف فيديو
function deleteVideo($pdo, $videoId) {
    try {
        $stmt = $pdo->prepare("DELETE FROM videos WHERE id = ?");
        return $stmt->execute([$videoId]);
    } catch(PDOException $e) {
        error_log("Error deleting video: " . $e->getMessage());
        return false;
    }
}

// تنسيق الأرقام
function formatNumber($number) {
    if ($number >= 1000000) {
        return round($number / 1000000, 1) . 'M';
    } elseif ($number >= 1000) {
        return round($number / 1000, 1) . 'K';
    }
    return number_format($number);
}

// حساب الوقت المنقضي
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة';
    if ($time < 86400) return floor($time/3600) . ' ساعة';
    if ($time < 2592000) return floor($time/86400) . ' يوم';
    if ($time < 31536000) return floor($time/2592000) . ' شهر';
    
    return floor($time/31536000) . ' سنة';
}

// تسجيل نشاط الإدارة
function logAdminActivity($pdo, $adminId, $action, $details = '') {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO admin_logs (admin_id, action, details, ip_address, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        return $stmt->execute([$adminId, $action, $details, $_SERVER['REMOTE_ADDR']]);
    } catch(PDOException $e) {
        error_log("Error logging admin activity: " . $e->getMessage());
        return false;
    }
}

// الحصول على إعدادات التطبيق
function getAppSettings($pdo) {
    try {
        $stmt = $pdo->query("SELECT setting_key, setting_value FROM app_settings");
        $settings = [];
        while ($row = $stmt->fetch()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        return $settings;
    } catch(PDOException $e) {
        error_log("Error getting app settings: " . $e->getMessage());
        return [];
    }
}

// تحديث إعداد التطبيق
function updateAppSetting($pdo, $key, $value) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO app_settings (setting_key, setting_value) 
            VALUES (?, ?) 
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
        ");
        return $stmt->execute([$key, $value]);
    } catch(PDOException $e) {
        error_log("Error updating app setting: " . $e->getMessage());
        return false;
    }
}
?>
