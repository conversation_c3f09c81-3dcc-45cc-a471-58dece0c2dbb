<nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top shadow">
    <div class="container-fluid">
        <!-- Brand -->
        <a class="navbar-brand d-flex align-items-center" href="index.php">
            <i class="fas fa-play me-2 text-primary"></i>
            <span class="fw-bold">Streamy Admin</span>
        </a>

        <!-- Mobile toggle -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navbar content -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <!-- Search -->
            <div class="navbar-nav me-auto">
                <form class="d-flex" role="search">
                    <div class="input-group">
                        <input class="form-control form-control-sm" type="search" placeholder="البحث..." aria-label="Search">
                        <button class="btn btn-outline-light btn-sm" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Right side items -->
            <ul class="navbar-nav">
                <!-- Notifications -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle position-relative" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <?php
                        // Get notification count
                        $notificationCount = 0;
                        try {
                            $stmt = $pdo->query("
                                SELECT COUNT(*) as count FROM (
                                    SELECT id FROM reports WHERE status = 'pending'
                                    UNION ALL
                                    SELECT id FROM videos WHERE is_approved = 0
                                ) as notifications
                            ");
                            $notificationCount = $stmt->fetch()['count'];
                        } catch(Exception $e) {
                            // Ignore errors
                        }
                        ?>
                        <?php if ($notificationCount > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?php echo $notificationCount > 99 ? '99+' : $notificationCount; ?>
                            </span>
                        <?php endif; ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">الإشعارات</h6></li>
                        <?php if ($notificationCount > 0): ?>
                            <li><a class="dropdown-item" href="reports.php">
                                <i class="fas fa-flag text-warning me-2"></i>
                                بلاغات جديدة
                            </a></li>
                            <li><a class="dropdown-item" href="videos.php?status=pending">
                                <i class="fas fa-video text-info me-2"></i>
                                فيديوهات في انتظار الموافقة
                            </a></li>
                        <?php else: ?>
                            <li><span class="dropdown-item-text text-muted">لا توجد إشعارات جديدة</span></li>
                        <?php endif; ?>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-center" href="notifications.php">عرض جميع الإشعارات</a></li>
                    </ul>
                </li>

                <!-- Quick Stats -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-chart-line"></i>
                        الإحصائيات
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">إحصائيات سريعة</h6></li>
                        <li><span class="dropdown-item-text">
                            <i class="fas fa-users text-primary me-2"></i>
                            المستخدمون: <?php echo formatNumber($stats['total_users'] ?? 0); ?>
                        </span></li>
                        <li><span class="dropdown-item-text">
                            <i class="fas fa-video text-success me-2"></i>
                            الفيديوهات: <?php echo formatNumber($stats['total_videos'] ?? 0); ?>
                        </span></li>
                        <li><span class="dropdown-item-text">
                            <i class="fas fa-eye text-info me-2"></i>
                            المشاهدات: <?php echo formatNumber($stats['total_views'] ?? 0); ?>
                        </span></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-center" href="analytics.php">عرض التحليلات المفصلة</a></li>
                    </ul>
                </li>

                <!-- User menu -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center me-2" 
                             style="width: 32px; height: 32px;">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <span><?php echo htmlspecialchars($_SESSION['admin_name'] ?? 'المدير'); ?></span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">
                            <?php echo htmlspecialchars($_SESSION['admin_name'] ?? 'المدير'); ?>
                            <br><small class="text-muted"><?php echo htmlspecialchars($_SESSION['admin_role'] ?? 'admin'); ?></small>
                        </h6></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="profile.php">
                            <i class="fas fa-user-cog me-2"></i>
                            الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="settings.php">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات
                        </a></li>
                        <li><a class="dropdown-item" href="activity-log.php">
                            <i class="fas fa-history me-2"></i>
                            سجل الأنشطة
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<style>
.navbar-brand {
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
    min-width: 250px;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.dropdown-header {
    font-weight: 600;
    color: #495057;
    padding: 0.75rem 1rem 0.5rem;
}

.dropdown-divider {
    margin: 0.5rem 0;
}

.badge {
    font-size: 0.7rem;
}

.input-group .form-control {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

@media (max-width: 991.98px) {
    .navbar-nav {
        padding-top: 1rem;
    }
    
    .navbar-nav .nav-link {
        margin: 0.25rem 0;
    }
    
    .dropdown-menu {
        min-width: 100%;
    }
}
</style>
