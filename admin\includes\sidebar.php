<?php
$current_page = basename($_SERVER['PHP_SELF']);

// Get notification counts
$pending_reports = 0;
$pending_videos = 0;
$inactive_users = 0;

try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM reports WHERE status = 'pending'");
    $pending_reports = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE is_approved = 0");
    $pending_videos = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM app_users WHERE is_active = 0");
    $inactive_users = $stmt->fetch()['count'];
} catch(Exception $e) {
    // Ignore errors
}
?>

<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <!-- Main Navigation -->
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'index.php' ? 'active' : ''; ?>" href="index.php">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    لوحة التحكم
                </a>
            </li>
        </ul>

        <!-- Content Management -->
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted text-uppercase">
            <span>إدارة المحتوى</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'users.php' ? 'active' : ''; ?>" href="users.php">
                    <i class="fas fa-users me-2"></i>
                    المستخدمون
                    <?php if ($inactive_users > 0): ?>
                        <span class="badge bg-warning rounded-pill ms-auto"><?php echo $inactive_users; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'videos.php' ? 'active' : ''; ?>" href="videos.php">
                    <i class="fas fa-video me-2"></i>
                    الفيديوهات
                    <?php if ($pending_videos > 0): ?>
                        <span class="badge bg-info rounded-pill ms-auto"><?php echo $pending_videos; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'comments.php' ? 'active' : ''; ?>" href="comments.php">
                    <i class="fas fa-comments me-2"></i>
                    التعليقات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'reports.php' ? 'active' : ''; ?>" href="reports.php">
                    <i class="fas fa-flag me-2"></i>
                    البلاغات
                    <?php if ($pending_reports > 0): ?>
                        <span class="badge bg-danger rounded-pill ms-auto"><?php echo $pending_reports; ?></span>
                    <?php endif; ?>
                </a>
            </li>
        </ul>

        <!-- Analytics -->
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted text-uppercase">
            <span>التحليلات والإحصائيات</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'analytics.php' ? 'active' : ''; ?>" href="analytics.php">
                    <i class="fas fa-chart-line me-2"></i>
                    التحليلات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'trending.php' ? 'active' : ''; ?>" href="trending.php">
                    <i class="fas fa-fire me-2"></i>
                    المحتوى الرائج
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'engagement.php' ? 'active' : ''; ?>" href="engagement.php">
                    <i class="fas fa-heart me-2"></i>
                    معدلات التفاعل
                </a>
            </li>
        </ul>

        <!-- System Management -->
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted text-uppercase">
            <span>إدارة النظام</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'admins.php' ? 'active' : ''; ?>" href="admins.php">
                    <i class="fas fa-user-shield me-2"></i>
                    المديرون
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'settings.php' ? 'active' : ''; ?>" href="settings.php">
                    <i class="fas fa-cog me-2"></i>
                    إعدادات التطبيق
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'backup.php' ? 'active' : ''; ?>" href="backup.php">
                    <i class="fas fa-database me-2"></i>
                    النسخ الاحتياطي
                </a>
            </li>
        </ul>

        <!-- Tools -->
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted text-uppercase">
            <span>الأدوات</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'notifications.php' ? 'active' : ''; ?>" href="notifications.php">
                    <i class="fas fa-bell me-2"></i>
                    إرسال إشعارات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'bulk-actions.php' ? 'active' : ''; ?>" href="bulk-actions.php">
                    <i class="fas fa-tasks me-2"></i>
                    العمليات المجمعة
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'logs.php' ? 'active' : ''; ?>" href="logs.php">
                    <i class="fas fa-list-alt me-2"></i>
                    سجل الأنشطة
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'help.php' ? 'active' : ''; ?>" href="help.php">
                    <i class="fas fa-question-circle me-2"></i>
                    المساعدة
                </a>
            </li>
        </ul>

        <!-- Quick Actions -->
        <div class="px-3 mt-4">
            <div class="card bg-primary text-white">
                <div class="card-body p-3">
                    <h6 class="card-title">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h6>
                    <div class="d-grid gap-2">
                        <button class="btn btn-light btn-sm" onclick="location.href='users.php?action=export'">
                            <i class="fas fa-download me-1"></i>
                            تصدير المستخدمين
                        </button>
                        <button class="btn btn-light btn-sm" onclick="location.href='videos.php?action=export'">
                            <i class="fas fa-download me-1"></i>
                            تصدير الفيديوهات
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="px-3 mt-3">
            <div class="card bg-light">
                <div class="card-body p-3">
                    <h6 class="card-title text-muted">
                        <i class="fas fa-server me-2"></i>
                        حالة النظام
                    </h6>
                    <div class="small">
                        <div class="d-flex justify-content-between">
                            <span>قاعدة البيانات:</span>
                            <span class="text-success">
                                <i class="fas fa-circle"></i> متصل
                            </span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>التخزين:</span>
                            <span class="text-success">
                                <i class="fas fa-circle"></i> طبيعي
                            </span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>آخر تحديث:</span>
                            <span class="text-muted">
                                <?php echo date('H:i'); ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>

<style>
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #f8f9fa;
    overflow-y: auto;
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.15s ease-in-out;
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover {
    color: #2470dc;
    background-color: rgba(36, 112, 220, 0.1);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    color: #2470dc;
    background-color: rgba(36, 112, 220, 0.15);
    font-weight: 600;
    border-left: 3px solid #2470dc;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-title {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.btn-sm {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
        position: relative;
        height: auto;
        padding: 1rem 0;
    }
    
    .sidebar .nav-link {
        margin: 0.125rem 1rem;
    }
}

/* Scrollbar styling */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
