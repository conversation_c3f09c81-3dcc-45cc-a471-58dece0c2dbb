<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
checkLogin();

// الحصول على الإحصائيات
$stats = getDashboardStats($pdo);
$recentUsers = getRecentUsers($pdo, 5);
$recentVideos = getRecentVideos($pdo, 5);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - Streamy Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-download me-1"></i>
                                تصدير التقرير
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-sync-alt me-1"></i>
                                تحديث
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-start-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            إجمالي المستخدمين
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo formatNumber($stats['total_users']); ?>
                                        </div>
                                        <div class="text-xs text-success">
                                            <i class="fas fa-arrow-up me-1"></i>
                                            <?php echo $stats['new_users_today']; ?> جديد اليوم
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-start-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            إجمالي الفيديوهات
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo formatNumber($stats['total_videos']); ?>
                                        </div>
                                        <div class="text-xs text-success">
                                            <i class="fas fa-arrow-up me-1"></i>
                                            <?php echo $stats['new_videos_today']; ?> جديد اليوم
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-video fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-start-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            إجمالي المشاهدات
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo formatNumber($stats['total_views']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-eye fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-start-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            إجمالي الإعجابات
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo formatNumber($stats['total_likes']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-heart fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Alerts Row -->
                <?php if ($stats['pending_reports'] > 0 || $stats['pending_videos'] > 0): ?>
                <div class="row mb-4">
                    <?php if ($stats['pending_reports'] > 0): ?>
                    <div class="col-md-6">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> يوجد <?php echo $stats['pending_reports']; ?> بلاغ في انتظار المراجعة
                            <a href="reports.php" class="alert-link">عرض البلاغات</a>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($stats['pending_videos'] > 0): ?>
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <i class="fas fa-clock me-2"></i>
                            <strong>معلومة:</strong> يوجد <?php echo $stats['pending_videos']; ?> فيديو في انتظار الموافقة
                            <a href="videos.php?status=pending" class="alert-link">مراجعة الفيديوهات</a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- Content Row -->
                <div class="row">
                    <!-- Recent Users -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-users me-2"></i>
                                    المستخدمون الجدد
                                </h6>
                                <a href="users.php" class="btn btn-sm btn-primary">عرض الكل</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recentUsers)): ?>
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-users fa-3x mb-3"></i>
                                        <p>لا يوجد مستخدمون جدد</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($recentUsers as $user): ?>
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="avatar me-3">
                                                <?php if ($user['profile_image']): ?>
                                                    <img src="<?php echo htmlspecialchars($user['profile_image']); ?>" 
                                                         alt="<?php echo htmlspecialchars($user['username']); ?>" 
                                                         class="rounded-circle" width="40" height="40">
                                                <?php else: ?>
                                                    <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" 
                                                         style="width: 40px; height: 40px;">
                                                        <i class="fas fa-user text-white"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="font-weight-bold">
                                                    <?php echo htmlspecialchars($user['full_name']); ?>
                                                    <?php if ($user['is_verified']): ?>
                                                        <i class="fas fa-check-circle text-primary ms-1" title="موثق"></i>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="text-muted small">@<?php echo htmlspecialchars($user['username']); ?></div>
                                            </div>
                                            <div class="text-muted small">
                                                <?php echo timeAgo($user['created_at']); ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Videos -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-video me-2"></i>
                                    الفيديوهات الأحدث
                                </h6>
                                <a href="videos.php" class="btn btn-sm btn-primary">عرض الكل</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recentVideos)): ?>
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-video fa-3x mb-3"></i>
                                        <p>لا توجد فيديوهات جديدة</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($recentVideos as $video): ?>
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="avatar me-3">
                                                <?php if ($video['thumbnail_url']): ?>
                                                    <img src="<?php echo htmlspecialchars($video['thumbnail_url']); ?>" 
                                                         alt="Video" class="rounded" width="60" height="40" style="object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="rounded bg-secondary d-flex align-items-center justify-content-center" 
                                                         style="width: 60px; height: 40px;">
                                                        <i class="fas fa-play text-white"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="font-weight-bold">
                                                    <?php echo htmlspecialchars(substr($video['caption'] ?: 'بدون وصف', 0, 50)); ?>
                                                    <?php if (strlen($video['caption']) > 50) echo '...'; ?>
                                                </div>
                                                <div class="text-muted small">
                                                    بواسطة @<?php echo htmlspecialchars($video['username'] ?: 'مجهول'); ?>
                                                    <?php if (!$video['is_approved']): ?>
                                                        <span class="badge bg-warning text-dark ms-1">في انتظار الموافقة</span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="text-muted small">
                                                    <i class="fas fa-eye me-1"></i><?php echo formatNumber($video['views_count']); ?>
                                                    <i class="fas fa-heart me-1 ms-2"></i><?php echo formatNumber($video['likes_count']); ?>
                                                </div>
                                            </div>
                                            <div class="text-muted small">
                                                <?php echo timeAgo($video['created_at']); ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-bolt me-2"></i>
                                    إجراءات سريعة
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <a href="users.php" class="btn btn-outline-primary w-100">
                                            <i class="fas fa-users fa-2x mb-2"></i><br>
                                            إدارة المستخدمين
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="videos.php" class="btn btn-outline-success w-100">
                                            <i class="fas fa-video fa-2x mb-2"></i><br>
                                            إدارة الفيديوهات
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="reports.php" class="btn btn-outline-warning w-100">
                                            <i class="fas fa-flag fa-2x mb-2"></i><br>
                                            مراجعة البلاغات
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="settings.php" class="btn btn-outline-info w-100">
                                            <i class="fas fa-cog fa-2x mb-2"></i><br>
                                            إعدادات التطبيق
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="assets/js/admin.js"></script>
</body>
</html>
