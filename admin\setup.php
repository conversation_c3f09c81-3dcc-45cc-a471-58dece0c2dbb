<?php
// ملف إعداد لوحة التحكم
$host = 'localhost';
$dbname = 'collectandwin2_hostmeed1';
$username = 'collectandwin2_hostmeed1';
$password = 'collectandwin2_hostmeed1';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إعداد لوحة التحكم - Streamy Admin</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem 0; }";
echo ".setup-container { max-width: 900px; margin: 0 auto; }";
echo ".card { box-shadow: 0 15px 35px rgba(0,0,0,0.1); border: none; border-radius: 15px; overflow: hidden; }";
echo ".card-header { background: linear-gradient(135deg, #FF0050 0%, #FF4081 100%); color: white; padding: 2rem; }";
echo ".step { margin: 1.5rem 0; padding: 1.5rem; border-radius: 10px; border-left: 4px solid #007bff; }";
echo ".step-success { background: #d4edda; border-left-color: #28a745; }";
echo ".step-error { background: #f8d7da; border-left-color: #dc3545; }";
echo ".step-warning { background: #fff3cd; border-left-color: #ffc107; }";
echo ".step-info { background: #d1ecf1; border-left-color: #17a2b8; }";
echo ".credentials { background: white; padding: 1.5rem; border-radius: 10px; margin: 1rem 0; border: 2px solid #FF0050; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container setup-container'>";
echo "<div class='card'>";
echo "<div class='card-header text-center'>";
echo "<h1><i class='fas fa-cogs me-3'></i>إعداد لوحة التحكم</h1>";
echo "<p class='mb-0 fs-5'>Streamy Admin Panel Setup</p>";
echo "</div>";
echo "<div class='card-body p-4'>";

$steps = [];
$hasError = false;

// الخطوة 1: اختبار الاتصال
echo "<div class='step step-info'>";
echo "<h4><i class='fas fa-database me-2'></i>الخطوة 1: الاتصال بقاعدة البيانات</h4>";

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // تعيين ترميز الاتصال
    $pdo->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("SET CHARACTER SET utf8mb4");
    $pdo->exec("SET character_set_connection=utf8mb4");
    echo "<p class='text-success fs-5'><i class='fas fa-check-circle me-2'></i>تم الاتصال بقاعدة البيانات بنجاح!</p>";
    echo "<p><strong>قاعدة البيانات:</strong> $dbname</p>";
    echo "<p><strong>الخادم:</strong> $host</p>";
    $steps[] = "✅ الاتصال بقاعدة البيانات";
} catch(PDOException $e) {
    echo "<p class='text-danger fs-5'><i class='fas fa-times-circle me-2'></i>فشل الاتصال بقاعدة البيانات</p>";
    echo "<p><strong>خطأ:</strong> " . $e->getMessage() . "</p>";
    $hasError = true;
    $steps[] = "❌ فشل الاتصال بقاعدة البيانات";
}
echo "</div>";

if (!$hasError) {
    // الخطوة 2: إنشاء الجداول
    echo "<div class='step step-info'>";
    echo "<h4><i class='fas fa-table me-2'></i>الخطوة 2: إنشاء الجداول</h4>";
    
    try {
        // تضمين ملف قاعدة البيانات لإنشاء الجداول
        require_once 'config/database.php';
        echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم إنشاء جميع الجداول بنجاح</p>";
        
        // عرض الجداول المنشأة
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p><strong>الجداول المنشأة:</strong></p>";
        echo "<ul class='list-unstyled'>";
        foreach ($tables as $table) {
            echo "<li><i class='fas fa-table text-success me-2'></i>$table</li>";
        }
        echo "</ul>";
        $steps[] = "✅ إنشاء الجداول";
    } catch(Exception $e) {
        echo "<p class='text-danger'><i class='fas fa-times me-2'></i>فشل في إنشاء الجداول</p>";
        echo "<p><strong>خطأ:</strong> " . $e->getMessage() . "</p>";
        $hasError = true;
        $steps[] = "❌ فشل في إنشاء الجداول";
    }
    echo "</div>";
}

if (!$hasError) {
    // الخطوة 3: التحقق من المستخدم الإداري
    echo "<div class='step step-info'>";
    echo "<h4><i class='fas fa-user-shield me-2'></i>الخطوة 3: المستخدم الإداري</h4>";
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE username = 'admin'");
        $stmt->execute();
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "<p class='text-success'><i class='fas fa-check me-2'></i>المستخدم الإداري موجود</p>";
            echo "<p><strong>اسم المستخدم:</strong> {$admin['username']}</p>";
            echo "<p><strong>الاسم الكامل:</strong> {$admin['full_name']}</p>";
            echo "<p><strong>الدور:</strong> {$admin['role']}</p>";
            echo "<p><strong>الحالة:</strong> " . ($admin['is_active'] ? 'مفعل' : 'غير مفعل') . "</p>";
            
            // اختبار كلمة المرور
            if (password_verify('Streamy@2024!', $admin['password_hash'])) {
                echo "<p class='text-success'><i class='fas fa-key me-2'></i>كلمة المرور صحيحة</p>";
                $steps[] = "✅ المستخدم الإداري جاهز";
            } else {
                echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-2'></i>كلمة المرور قد تحتاج إعادة تعيين</p>";
                $steps[] = "⚠️ كلمة المرور تحتاج مراجعة";
            }
        } else {
            echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-2'></i>المستخدم الإداري غير موجود</p>";
            $steps[] = "⚠️ المستخدم الإداري غير موجود";
        }
    } catch(Exception $e) {
        echo "<p class='text-danger'><i class='fas fa-times me-2'></i>خطأ في فحص المستخدم الإداري</p>";
        echo "<p><strong>خطأ:</strong> " . $e->getMessage() . "</p>";
        $steps[] = "❌ خطأ في فحص المستخدم الإداري";
    }
    echo "</div>";
}

if (!$hasError) {
    // الخطوة 4: اختبار الإعدادات
    echo "<div class='step step-info'>";
    echo "<h4><i class='fas fa-cog me-2'></i>الخطوة 4: إعدادات التطبيق</h4>";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM app_settings");
        $settingsCount = $stmt->fetch()['count'];
        
        if ($settingsCount > 0) {
            echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم تحميل $settingsCount إعداد</p>";
            
            // عرض بعض الإعدادات
            $stmt = $pdo->query("SELECT setting_key, setting_value FROM app_settings LIMIT 5");
            $settings = $stmt->fetchAll();
            echo "<p><strong>الإعدادات المحملة:</strong></p>";
            echo "<ul class='list-unstyled'>";
            foreach ($settings as $setting) {
                echo "<li><i class='fas fa-cog text-info me-2'></i>{$setting['setting_key']}: {$setting['setting_value']}</li>";
            }
            echo "</ul>";
            $steps[] = "✅ إعدادات التطبيق";
        } else {
            echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-2'></i>لا توجد إعدادات</p>";
            $steps[] = "⚠️ لا توجد إعدادات";
        }
    } catch(Exception $e) {
        echo "<p class='text-danger'><i class='fas fa-times me-2'></i>خطأ في فحص الإعدادات</p>";
        $steps[] = "❌ خطأ في فحص الإعدادات";
    }
    echo "</div>";
}

// ملخص النتائج
echo "<div class='step " . ($hasError ? 'step-error' : 'step-success') . "'>";
echo "<h4><i class='fas fa-clipboard-check me-2'></i>ملخص الإعداد</h4>";
echo "<ul class='list-unstyled fs-5'>";
foreach ($steps as $step) {
    echo "<li class='mb-2'>$step</li>";
}
echo "</ul>";

if (!$hasError) {
    echo "<div class='credentials'>";
    echo "<h5 class='text-center mb-4'><i class='fas fa-key me-2'></i>بيانات تسجيل الدخول</h5>";
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<p class='mb-2'><strong>🌐 رابط لوحة التحكم:</strong></p>";
    echo "<p class='text-primary fs-6'>https://xyz.collectandwin.xyz/admin/</p>";
    echo "</div>";
    echo "<div class='col-md-6'>";
    echo "<p class='mb-2'><strong>👤 اسم المستخدم:</strong> admin</p>";
    echo "<p class='mb-2'><strong>🔐 كلمة المرور:</strong> Streamy@2024!</p>";
    echo "</div>";
    echo "</div>";
    echo "<div class='text-center mt-4'>";
    echo "<a href='login.php' class='btn btn-primary btn-lg px-5'>";
    echo "<i class='fas fa-sign-in-alt me-2'></i>دخول لوحة التحكم";
    echo "</a>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div class='alert alert-danger mt-4'>";
    echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>فشل في الإعداد!</h5>";
    echo "<p>يرجى مراجعة الأخطاء أعلاه وإصلاحها قبل المتابعة.</p>";
    echo "<button onclick='location.reload()' class='btn btn-warning'>";
    echo "<i class='fas fa-redo me-2'></i>إعادة المحاولة";
    echo "</button>";
    echo "</div>";
}
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
