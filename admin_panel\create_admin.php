<?php
// ملف إنشاء المستخدم الإداري بشكل مباشر
$host = 'localhost';
$dbname = 'streamy_db';
$username = 'root';
$password = '';

echo "<h2>إنشاء قاعدة البيانات والمستخدم الإداري</h2>";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    echo "<p>🔄 محاولة الاتصال بـ MySQL...</p>";
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p>✅ تم الاتصال بـ MySQL بنجاح</p>";

    // إنشاء قاعدة البيانات
    echo "<p>🔄 إنشاء قاعدة البيانات...</p>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $dbname CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✅ تم إنشاء قاعدة البيانات '$dbname'</p>";

    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // إنشاء جدول المستخدمين الإداريين
    echo "<p>🔄 إنشاء جدول المستخدمين الإداريين...</p>";
    $createAdminTable = "
    CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($createAdminTable);
    echo "<p>✅ تم إنشاء جدول admin_users</p>";

    // حذف المستخدم الموجود (إن وجد) وإنشاء جديد
    echo "<p>🔄 إنشاء المستخدم الإداري...</p>";
    $pdo->exec("DELETE FROM admin_users WHERE username = 'admin'");
    
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("
        INSERT INTO admin_users (username, email, password_hash, full_name, role, is_active) 
        VALUES ('admin', '<EMAIL>', ?, 'مدير النظام', 'super_admin', 1)
    ");
    $stmt->execute([$adminPassword]);
    echo "<p>✅ تم إنشاء المستخدم الإداري بنجاح!</p>";

    // اختبار كلمة المرور
    echo "<p>🔄 اختبار كلمة المرور...</p>";
    $stmt = $pdo->prepare("SELECT password_hash FROM admin_users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin && password_verify('admin123', $admin['password_hash'])) {
        echo "<p>✅ كلمة المرور تعمل بشكل صحيح!</p>";
    } else {
        echo "<p>❌ مشكلة في كلمة المرور!</p>";
    }

    // عرض بيانات المستخدم
    echo "<h3>📋 بيانات تسجيل الدخول:</h3>";
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>اسم المستخدم:</strong> admin<br>";
    echo "<strong>كلمة المرور:</strong> admin123<br>";
    echo "<strong>البريد الإلكتروني:</strong> <EMAIL>";
    echo "</div>";

    echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب إلى صفحة تسجيل الدخول</a></p>";

} catch(PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>صحة بيانات الاتصال في أعلى الملف</li>";
    echo "<li>وجود صلاحيات إنشاء قواعد البيانات</li>";
    echo "</ul>";
}
?>
