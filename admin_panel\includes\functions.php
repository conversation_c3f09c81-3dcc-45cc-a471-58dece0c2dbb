<?php
// Get dashboard statistics
function getDashboardStats($pdo) {
    $stats = [];
    
    // Total users
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
    $stats['total_users'] = $stmt->fetch()['count'];
    
    // Total videos
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE is_approved = 1");
    $stats['total_videos'] = $stmt->fetch()['count'];
    
    // Total views
    $stmt = $pdo->query("SELECT SUM(views_count) as total FROM videos");
    $stats['total_views'] = $stmt->fetch()['total'] ?: 0;
    
    // Total likes
    $stmt = $pdo->query("SELECT SUM(likes_count) as total FROM videos");
    $stats['total_likes'] = $stmt->fetch()['total'] ?: 0;
    
    return $stats;
}

// Get recent users
function getRecentUsers($pdo, $limit = 10) {
    $stmt = $pdo->prepare("
        SELECT id, firebase_uid, username, full_name, profile_image, created_at 
        FROM users 
        WHERE is_active = 1 
        ORDER BY created_at DESC 
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    return $stmt->fetchAll();
}

// Get recent videos
function getRecentVideos($pdo, $limit = 10) {
    $stmt = $pdo->prepare("
        SELECT v.id, v.firebase_id, v.caption, v.thumbnail_url as thumbnail, v.views_count, v.likes_count, v.created_at,
               u.username, u.full_name
        FROM videos v
        JOIN users u ON v.user_id = u.id
        WHERE v.is_approved = 1 AND u.is_active = 1
        ORDER BY v.created_at DESC
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    return $stmt->fetchAll();
}

// Get all users with pagination
function getUsers($pdo, $page = 1, $limit = 20, $search = '') {
    $offset = ($page - 1) * $limit;
    
    $whereClause = "WHERE 1=1";
    $params = [];
    
    if (!empty($search)) {
        $whereClause .= " AND (username LIKE ? OR full_name LIKE ? OR email LIKE ?)";
        $searchTerm = "%$search%";
        $params = [$searchTerm, $searchTerm, $searchTerm];
    }
    
    // Get total count
    $countStmt = $pdo->prepare("SELECT COUNT(*) as total FROM users $whereClause");
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];
    
    // Get users
    $stmt = $pdo->prepare("
        SELECT id, firebase_uid, username, full_name, email, profile_image, 
               followers_count, following_count, likes_count, is_verified, is_active, created_at
        FROM users 
        $whereClause
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
    ");
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $users = $stmt->fetchAll();
    
    return [
        'users' => $users,
        'total' => $total,
        'pages' => ceil($total / $limit),
        'current_page' => $page
    ];
}

// Get all videos with pagination
function getVideos($pdo, $page = 1, $limit = 20, $search = '', $status = 'all') {
    $offset = ($page - 1) * $limit;
    
    $whereClause = "WHERE 1=1";
    $params = [];
    
    if (!empty($search)) {
        $whereClause .= " AND (v.caption LIKE ? OR u.username LIKE ?)";
        $searchTerm = "%$search%";
        $params = [$searchTerm, $searchTerm];
    }
    
    if ($status !== 'all') {
        if ($status === 'approved') {
            $whereClause .= " AND v.is_approved = 1";
        } elseif ($status === 'pending') {
            $whereClause .= " AND v.is_approved = 0";
        }
    }
    
    // Get total count
    $countStmt = $pdo->prepare("
        SELECT COUNT(*) as total 
        FROM videos v 
        JOIN users u ON v.user_id = u.id 
        $whereClause
    ");
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];
    
    // Get videos
    $stmt = $pdo->prepare("
        SELECT v.id, v.firebase_id, v.caption, v.video_url, v.thumbnail_url, 
               v.likes_count, v.comments_count, v.views_count, v.shares_count,
               v.is_public, v.is_approved, v.created_at,
               u.username, u.full_name, u.profile_image
        FROM videos v
        JOIN users u ON v.user_id = u.id
        $whereClause
        ORDER BY v.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $videos = $stmt->fetchAll();
    
    return [
        'videos' => $videos,
        'total' => $total,
        'pages' => ceil($total / $limit),
        'current_page' => $page
    ];
}

// Get reports with pagination
function getReports($pdo, $page = 1, $limit = 20, $status = 'all') {
    $offset = ($page - 1) * $limit;
    
    $whereClause = "WHERE 1=1";
    $params = [];
    
    if ($status !== 'all') {
        $whereClause .= " AND r.status = ?";
        $params[] = $status;
    }
    
    // Get total count
    $countStmt = $pdo->prepare("SELECT COUNT(*) as total FROM reports r $whereClause");
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];
    
    // Get reports
    $stmt = $pdo->prepare("
        SELECT r.id, r.report_type, r.description, r.status, r.created_at,
               reporter.username as reporter_username,
               reported_user.username as reported_username,
               v.caption as video_caption
        FROM reports r
        LEFT JOIN users reporter ON r.reporter_id = reporter.id
        LEFT JOIN users reported_user ON r.reported_user_id = reported_user.id
        LEFT JOIN videos v ON r.reported_video_id = v.id
        $whereClause
        ORDER BY r.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $reports = $stmt->fetchAll();
    
    return [
        'reports' => $reports,
        'total' => $total,
        'pages' => ceil($total / $limit),
        'current_page' => $page
    ];
}

// Update user status
function updateUserStatus($pdo, $userId, $status) {
    $stmt = $pdo->prepare("UPDATE users SET is_active = ? WHERE id = ?");
    return $stmt->execute([$status, $userId]);
}

// Update video approval status
function updateVideoApproval($pdo, $videoId, $status) {
    $stmt = $pdo->prepare("UPDATE videos SET is_approved = ? WHERE id = ?");
    return $stmt->execute([$status, $videoId]);
}

// Delete video
function deleteVideo($pdo, $videoId) {
    $stmt = $pdo->prepare("DELETE FROM videos WHERE id = ?");
    return $stmt->execute([$videoId]);
}

// Update report status
function updateReportStatus($pdo, $reportId, $status) {
    $stmt = $pdo->prepare("UPDATE reports SET status = ?, updated_at = NOW() WHERE id = ?");
    return $stmt->execute([$status, $reportId]);
}

// Get app settings
function getAppSettings($pdo) {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM app_settings");
    $settings = [];
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
    return $settings;
}

// Update app setting
function updateAppSetting($pdo, $key, $value) {
    $stmt = $pdo->prepare("
        INSERT INTO app_settings (setting_key, setting_value) 
        VALUES (?, ?) 
        ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
    ");
    return $stmt->execute([$key, $value]);
}

// Get analytics data
function getAnalyticsData($pdo, $days = 30) {
    $stmt = $pdo->prepare("
        SELECT date, new_users, new_videos, total_views, total_likes, active_users
        FROM analytics 
        WHERE date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
        ORDER BY date ASC
    ");
    $stmt->execute([$days]);
    return $stmt->fetchAll();
}

// Time ago function
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة';
    if ($time < 86400) return floor($time/3600) . ' ساعة';
    if ($time < 2592000) return floor($time/86400) . ' يوم';
    if ($time < 31536000) return floor($time/2592000) . ' شهر';
    
    return floor($time/31536000) . ' سنة';
}

// Format number
function formatNumber($number) {
    if ($number >= 1000000) {
        return round($number / 1000000, 1) . 'M';
    } elseif ($number >= 1000) {
        return round($number / 1000, 1) . 'K';
    }
    return number_format($number);
}

// Check admin permission
function checkAdminPermission($role, $required_role) {
    $roles = ['moderator' => 1, 'admin' => 2, 'super_admin' => 3];
    return $roles[$role] >= $roles[$required_role];
}

// Log admin action
function logAdminAction($pdo, $admin_id, $action, $details = '') {
    $stmt = $pdo->prepare("
        INSERT INTO admin_logs (admin_id, action, details, created_at) 
        VALUES (?, ?, ?, NOW())
    ");
    return $stmt->execute([$admin_id, $action, $details]);
}
?>
