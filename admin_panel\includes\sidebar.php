<?php
$current_page = basename($_SERVER['PHP_SELF']);
?>

<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'index.php' ? 'active' : ''; ?>" 
                   href="index.php">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    لوحة التحكم
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'users.php' ? 'active' : ''; ?>" 
                   href="users.php">
                    <i class="fas fa-users me-2"></i>
                    المستخدمون
                    <?php
                    // Get pending users count
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE is_active = 0");
                    $pending_users = $stmt->fetch()['count'];
                    if ($pending_users > 0): ?>
                        <span class="badge bg-warning rounded-pill ms-auto"><?php echo $pending_users; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'videos.php' ? 'active' : ''; ?>" 
                   href="videos.php">
                    <i class="fas fa-video me-2"></i>
                    الفيديوهات
                    <?php
                    // Get pending videos count
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE is_approved = 0");
                    $pending_videos = $stmt->fetch()['count'];
                    if ($pending_videos > 0): ?>
                        <span class="badge bg-warning rounded-pill ms-auto"><?php echo $pending_videos; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'comments.php' ? 'active' : ''; ?>" 
                   href="comments.php">
                    <i class="fas fa-comments me-2"></i>
                    التعليقات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'reports.php' ? 'active' : ''; ?>" 
                   href="reports.php">
                    <i class="fas fa-flag me-2"></i>
                    البلاغات
                    <?php
                    // Get pending reports count
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM reports WHERE status = 'pending'");
                    $pending_reports = $stmt->fetch()['count'];
                    if ($pending_reports > 0): ?>
                        <span class="badge bg-danger rounded-pill ms-auto"><?php echo $pending_reports; ?></span>
                    <?php endif; ?>
                </a>
            </li>
        </ul>

        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted text-uppercase">
            <span>الإحصائيات</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'analytics.php' ? 'active' : ''; ?>" 
                   href="analytics.php">
                    <i class="fas fa-chart-line me-2"></i>
                    التحليلات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'trending.php' ? 'active' : ''; ?>" 
                   href="trending.php">
                    <i class="fas fa-fire me-2"></i>
                    الترندات
                </a>
            </li>
        </ul>

        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted text-uppercase">
            <span>الإدارة</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'admins.php' ? 'active' : ''; ?>" 
                   href="admins.php">
                    <i class="fas fa-user-shield me-2"></i>
                    المديرون
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'settings.php' ? 'active' : ''; ?>" 
                   href="settings.php">
                    <i class="fas fa-cog me-2"></i>
                    الإعدادات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'backup.php' ? 'active' : ''; ?>" 
                   href="backup.php">
                    <i class="fas fa-database me-2"></i>
                    النسخ الاحتياطي
                </a>
            </li>
        </ul>

        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted text-uppercase">
            <span>الأدوات</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link" href="logs.php">
                    <i class="fas fa-list-alt me-2"></i>
                    سجل الأنشطة
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="notifications.php">
                    <i class="fas fa-bell me-2"></i>
                    الإشعارات
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="help.php">
                    <i class="fas fa-question-circle me-2"></i>
                    المساعدة
                </a>
            </li>
        </ul>
    </div>
</nav>

<style>
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.15s ease-in-out;
}

.sidebar .nav-link:hover {
    color: #2470dc;
    background-color: rgba(36, 112, 220, 0.1);
}

.sidebar .nav-link.active {
    color: #2470dc;
    background-color: rgba(36, 112, 220, 0.1);
    font-weight: 600;
}

.sidebar .nav-link i {
    width: 16px;
    text-align: center;
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
    }
}
</style>
