<?php
// صفحة إعداد قاعدة البيانات الأولي
$host = 'localhost';
$dbname = 'streamy_db';
$username = 'root';
$password = '';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إعداد قاعدة البيانات - Streamy Admin</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }";
echo ".setup-container { max-width: 800px; margin: 2rem auto; }";
echo ".card { box-shadow: 0 10px 30px rgba(0,0,0,0.2); border: none; border-radius: 15px; }";
echo ".card-header { background: linear-gradient(135deg, #FF0050 0%, #FF4081 100%); color: white; border-radius: 15px 15px 0 0; }";
echo ".step { margin: 1rem 0; padding: 1rem; border-radius: 10px; }";
echo ".step-success { background: #d4edda; border: 1px solid #c3e6cb; }";
echo ".step-error { background: #f8d7da; border: 1px solid #f5c6cb; }";
echo ".step-info { background: #d1ecf1; border: 1px solid #bee5eb; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container setup-container'>";
echo "<div class='card'>";
echo "<div class='card-header text-center'>";
echo "<h2><i class='fas fa-cogs'></i> إعداد قاعدة البيانات - Streamy</h2>";
echo "<p class='mb-0'>إعداد أولي لقاعدة البيانات ولوحة التحكم</p>";
echo "</div>";
echo "<div class='card-body'>";

$steps = [];
$hasError = false;

// الخطوة 1: اختبار الاتصال
echo "<div class='step step-info'>";
echo "<h5><i class='fas fa-database'></i> الخطوة 1: اختبار الاتصال بقاعدة البيانات</h5>";

try {
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='text-success'><i class='fas fa-check'></i> تم الاتصال بخادم MySQL بنجاح</p>";
    $steps[] = "✅ الاتصال بخادم MySQL";
} catch(PDOException $e) {
    echo "<p class='text-danger'><i class='fas fa-times'></i> فشل الاتصال بخادم MySQL: " . $e->getMessage() . "</p>";
    $hasError = true;
    $steps[] = "❌ فشل الاتصال بخادم MySQL";
}
echo "</div>";

if (!$hasError) {
    // الخطوة 2: إنشاء قاعدة البيانات
    echo "<div class='step step-info'>";
    echo "<h5><i class='fas fa-plus-circle'></i> الخطوة 2: إنشاء قاعدة البيانات</h5>";
    
    try {
        $pdo->exec("CREATE DATABASE IF NOT EXISTS $dbname CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p class='text-success'><i class='fas fa-check'></i> تم إنشاء قاعدة البيانات '$dbname' بنجاح</p>";
        $steps[] = "✅ إنشاء قاعدة البيانات";
        
        // الاتصال بقاعدة البيانات الجديدة
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
    } catch(PDOException $e) {
        echo "<p class='text-danger'><i class='fas fa-times'></i> فشل في إنشاء قاعدة البيانات: " . $e->getMessage() . "</p>";
        $hasError = true;
        $steps[] = "❌ فشل في إنشاء قاعدة البيانات";
    }
    echo "</div>";
}

if (!$hasError) {
    // الخطوة 3: إنشاء الجداول
    echo "<div class='step step-info'>";
    echo "<h5><i class='fas fa-table'></i> الخطوة 3: إنشاء الجداول</h5>";
    
    $createTables = "
    -- جدول المستخدمين الإداريين
    CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );

    -- جدول المستخدمين
    CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        firebase_uid VARCHAR(255) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        username VARCHAR(50) UNIQUE NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        profile_image VARCHAR(500),
        bio TEXT,
        followers_count INT DEFAULT 0,
        following_count INT DEFAULT 0,
        likes_count INT DEFAULT 0,
        is_verified BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );

    -- جدول الفيديوهات
    CREATE TABLE IF NOT EXISTS videos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        firebase_id VARCHAR(255) UNIQUE NOT NULL,
        user_id INT NOT NULL,
        video_url VARCHAR(500) NOT NULL,
        thumbnail_url VARCHAR(500),
        caption TEXT,
        hashtags JSON,
        likes_count INT DEFAULT 0,
        comments_count INT DEFAULT 0,
        shares_count INT DEFAULT 0,
        views_count INT DEFAULT 0,
        duration DECIMAL(10,2) DEFAULT 0,
        is_public BOOLEAN DEFAULT TRUE,
        is_approved BOOLEAN DEFAULT TRUE,
        music_name VARCHAR(255),
        music_url VARCHAR(500),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    );

    -- جدول الإعدادات
    CREATE TABLE IF NOT EXISTS app_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        description TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );
    ";
    
    try {
        $pdo->exec($createTables);
        echo "<p class='text-success'><i class='fas fa-check'></i> تم إنشاء جميع الجداول بنجاح</p>";
        $steps[] = "✅ إنشاء الجداول";
    } catch(PDOException $e) {
        echo "<p class='text-danger'><i class='fas fa-times'></i> فشل في إنشاء الجداول: " . $e->getMessage() . "</p>";
        $hasError = true;
        $steps[] = "❌ فشل في إنشاء الجداول";
    }
    echo "</div>";
}

if (!$hasError) {
    // الخطوة 4: إنشاء المستخدم الافتراضي
    echo "<div class='step step-info'>";
    echo "<h5><i class='fas fa-user-shield'></i> الخطوة 4: إنشاء المستخدم الإداري الافتراضي</h5>";
    
    try {
        // فحص وجود المستخدم
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM admin_users WHERE username = 'admin'");
        $stmt->execute();
        
        if ($stmt->fetchColumn() == 0) {
            $defaultPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO admin_users (username, email, password_hash, full_name, role, is_active) 
                VALUES ('admin', '<EMAIL>', ?, 'مدير النظام', 'super_admin', 1)
            ");
            $stmt->execute([$defaultPassword]);
            echo "<p class='text-success'><i class='fas fa-check'></i> تم إنشاء المستخدم الإداري الافتراضي</p>";
        } else {
            echo "<p class='text-info'><i class='fas fa-info'></i> المستخدم الإداري موجود بالفعل</p>";
        }
        $steps[] = "✅ المستخدم الإداري الافتراضي";
    } catch(PDOException $e) {
        echo "<p class='text-danger'><i class='fas fa-times'></i> فشل في إنشاء المستخدم الإداري: " . $e->getMessage() . "</p>";
        $hasError = true;
        $steps[] = "❌ فشل في إنشاء المستخدم الإداري";
    }
    echo "</div>";
}

if (!$hasError) {
    // الخطوة 5: إضافة الإعدادات الافتراضية
    echo "<div class='step step-info'>";
    echo "<h5><i class='fas fa-cog'></i> الخطوة 5: إضافة الإعدادات الافتراضية</h5>";
    
    $defaultSettings = [
        ['app_name', 'Streamy', 'اسم التطبيق'],
        ['max_video_duration', '180', 'أقصى مدة للفيديو بالثواني'],
        ['max_file_size', '100', 'أقصى حجم للملف بالميجابايت'],
        ['auto_approve_videos', '1', 'الموافقة التلقائية على الفيديوهات'],
        ['maintenance_mode', '0', 'وضع الصيانة'],
    ];
    
    try {
        foreach ($defaultSettings as $setting) {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO app_settings (setting_key, setting_value, description) 
                VALUES (?, ?, ?)
            ");
            $stmt->execute($setting);
        }
        echo "<p class='text-success'><i class='fas fa-check'></i> تم إضافة الإعدادات الافتراضية</p>";
        $steps[] = "✅ الإعدادات الافتراضية";
    } catch(PDOException $e) {
        echo "<p class='text-warning'><i class='fas fa-exclamation-triangle'></i> تحذير في إضافة الإعدادات: " . $e->getMessage() . "</p>";
        $steps[] = "⚠️ تحذير في الإعدادات";
    }
    echo "</div>";
}

// ملخص النتائج
echo "<div class='step " . ($hasError ? 'step-error' : 'step-success') . "'>";
echo "<h5><i class='fas fa-clipboard-check'></i> ملخص الإعداد</h5>";
echo "<ul>";
foreach ($steps as $step) {
    echo "<li>$step</li>";
}
echo "</ul>";

if (!$hasError) {
    echo "<div class='alert alert-success mt-3'>";
    echo "<h6><i class='fas fa-party-horn'></i> تم الإعداد بنجاح!</h6>";
    echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
    echo "<ul>";
    echo "<li><strong>اسم المستخدم:</strong> admin</li>";
    echo "<li><strong>كلمة المرور:</strong> admin123</li>";
    echo "</ul>";
    echo "<a href='login.php' class='btn btn-success btn-lg'>";
    echo "<i class='fas fa-sign-in-alt'></i> الذهاب إلى لوحة التحكم";
    echo "</a>";
    echo "</div>";
} else {
    echo "<div class='alert alert-danger mt-3'>";
    echo "<h6><i class='fas fa-exclamation-triangle'></i> فشل في الإعداد!</h6>";
    echo "<p>يرجى مراجعة الأخطاء أعلاه وإصلاحها قبل المتابعة.</p>";
    echo "<button onclick='location.reload()' class='btn btn-warning'>";
    echo "<i class='fas fa-redo'></i> إعادة المحاولة";
    echo "</button>";
    echo "</div>";
}
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
