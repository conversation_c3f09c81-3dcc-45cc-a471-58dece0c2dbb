<?php
// صفحة اختبار قاعدة البيانات
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار قاعدة البيانات - Streamy</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }";
echo ".container { margin-top: 2rem; }";
echo ".card { margin-bottom: 1rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo ".info { color: #17a2b8; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-10'>";

echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-database'></i> اختبار قاعدة البيانات - Streamy</h3>";
echo "</div>";
echo "<div class='card-body'>";

try {
    echo "<div class='alert alert-success'>";
    echo "<h5><i class='fas fa-check-circle'></i> تم الاتصال بقاعدة البيانات بنجاح!</h5>";
    echo "</div>";

    // اختبار الجداول
    echo "<h4>📋 فحص الجداول:</h4>";
    $tables = ['admin_users', 'users', 'videos', 'comments', 'likes', 'follows', 'reports', 'app_settings'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            echo "<div class='alert alert-info'>";
            echo "✅ جدول <strong>$table</strong>: موجود ويحتوي على <strong>$count</strong> سجل";
            echo "</div>";
        } catch (Exception $e) {
            echo "<div class='alert alert-warning'>";
            echo "⚠️ جدول <strong>$table</strong>: غير موجود أو يحتوي على خطأ";
            echo "</div>";
        }
    }

    // فحص المستخدمين الإداريين
    echo "<h4>👥 المستخدمون الإداريون:</h4>";
    $stmt = $pdo->query("SELECT * FROM admin_users");
    $admins = $stmt->fetchAll();
    
    if (empty($admins)) {
        echo "<div class='alert alert-warning'>";
        echo "⚠️ لا يوجد مستخدمون إداريون في قاعدة البيانات";
        echo "</div>";
        
        // إنشاء مستخدم افتراضي
        echo "<h5>🔧 إنشاء مستخدم افتراضي:</h5>";
        $defaultPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO admin_users (username, email, password_hash, full_name, role, is_active) 
            VALUES ('admin', '<EMAIL>', ?, 'مدير النظام', 'super_admin', 1)
        ");
        
        if ($stmt->execute([$defaultPassword])) {
            echo "<div class='alert alert-success'>";
            echo "✅ تم إنشاء المستخدم الافتراضي بنجاح!<br>";
            echo "<strong>اسم المستخدم:</strong> admin<br>";
            echo "<strong>كلمة المرور:</strong> admin123";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "❌ فشل في إنشاء المستخدم الافتراضي";
            echo "</div>";
        }
    } else {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead class='table-dark'>";
        echo "<tr><th>ID</th><th>اسم المستخدم</th><th>البريد الإلكتروني</th><th>الاسم الكامل</th><th>الدور</th><th>الحالة</th><th>آخر دخول</th></tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($admins as $admin) {
            $statusClass = $admin['is_active'] ? 'success' : 'danger';
            $statusText = $admin['is_active'] ? 'مفعل' : 'غير مفعل';
            $lastLogin = $admin['last_login'] ? date('Y-m-d H:i', strtotime($admin['last_login'])) : 'لم يسجل دخول';
            
            echo "<tr>";
            echo "<td>{$admin['id']}</td>";
            echo "<td><strong>{$admin['username']}</strong></td>";
            echo "<td>{$admin['email']}</td>";
            echo "<td>{$admin['full_name']}</td>";
            echo "<td><span class='badge bg-primary'>{$admin['role']}</span></td>";
            echo "<td><span class='badge bg-$statusClass'>$statusText</span></td>";
            echo "<td><small>$lastLogin</small></td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    }

    // اختبار كلمة المرور
    echo "<h4>🔐 اختبار كلمة المرور:</h4>";
    $stmt = $pdo->prepare("SELECT password_hash FROM admin_users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin && password_verify('admin123', $admin['password_hash'])) {
        echo "<div class='alert alert-success'>";
        echo "✅ كلمة المرور صحيحة للمستخدم admin";
        echo "</div>";
    } else {
        echo "<div class='alert alert-danger'>";
        echo "❌ كلمة المرور غير صحيحة أو المستخدم غير موجود";
        echo "</div>";
        
        // إعادة تعيين كلمة المرور
        if ($admin) {
            $newPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE admin_users SET password_hash = ? WHERE username = 'admin'");
            if ($stmt->execute([$newPassword])) {
                echo "<div class='alert alert-info'>";
                echo "🔄 تم إعادة تعيين كلمة المرور إلى: admin123";
                echo "</div>";
            }
        }
    }

    // معلومات قاعدة البيانات
    echo "<h4>ℹ️ معلومات قاعدة البيانات:</h4>";
    $stmt = $pdo->query("SELECT DATABASE() as db_name");
    $dbInfo = $stmt->fetch();
    
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<ul class='list-group'>";
    echo "<li class='list-group-item'><strong>اسم قاعدة البيانات:</strong> {$dbInfo['db_name']}</li>";
    echo "<li class='list-group-item'><strong>الخادم:</strong> $host</li>";
    echo "<li class='list-group-item'><strong>المستخدم:</strong> $username</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5><i class='fas fa-exclamation-triangle'></i> خطأ في الاتصال!</h5>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

echo "<div class='card'>";
echo "<div class='card-body text-center'>";
echo "<a href='login.php' class='btn btn-primary btn-lg'>";
echo "<i class='fas fa-sign-in-alt'></i> الذهاب إلى صفحة تسجيل الدخول";
echo "</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "<script src='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js'></script>";
echo "</body>";
echo "</html>";
?>
