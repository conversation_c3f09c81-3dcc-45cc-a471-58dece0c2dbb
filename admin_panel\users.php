<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit();
}

// Handle actions
if ($_POST) {
    if (isset($_POST['action']) && isset($_POST['user_id'])) {
        $user_id = (int)$_POST['user_id'];
        
        switch ($_POST['action']) {
            case 'activate':
                updateUserStatus($pdo, $user_id, 1);
                $message = 'تم تفعيل المستخدم بنجاح';
                break;
            case 'deactivate':
                updateUserStatus($pdo, $user_id, 0);
                $message = 'تم إلغاء تفعيل المستخدم بنجاح';
                break;
            case 'verify':
                $stmt = $pdo->prepare("UPDATE users SET is_verified = 1 WHERE id = ?");
                $stmt->execute([$user_id]);
                $message = 'تم توثيق المستخدم بنجاح';
                break;
            case 'unverify':
                $stmt = $pdo->prepare("UPDATE users SET is_verified = 0 WHERE id = ?");
                $stmt->execute([$user_id]);
                $message = 'تم إلغاء توثيق المستخدم بنجاح';
                break;
        }
    }
}

// Get parameters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status = isset($_GET['status']) ? $_GET['status'] : 'all';

// Get users
$users_data = getUsers($pdo, $page, 20, $search);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - لوحة تحكم Streamy</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة المستخدمين</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-download"></i> تصدير
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (isset($message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Filters -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <form method="GET" class="d-flex">
                            <input type="text" class="form-control me-2" name="search" 
                                   placeholder="البحث بالاسم أو اسم المستخدم أو البريد الإلكتروني..." 
                                   value="<?php echo htmlspecialchars($search); ?>">
                            <button class="btn btn-outline-secondary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" onchange="filterUsers(this.value)">
                            <option value="all" <?php echo $status == 'all' ? 'selected' : ''; ?>>جميع المستخدمين</option>
                            <option value="active" <?php echo $status == 'active' ? 'selected' : ''; ?>>المفعلون</option>
                            <option value="inactive" <?php echo $status == 'inactive' ? 'selected' : ''; ?>>غير المفعلين</option>
                            <option value="verified" <?php echo $status == 'verified' ? 'selected' : ''; ?>>الموثقون</option>
                        </select>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            قائمة المستخدمين 
                            <span class="badge bg-primary"><?php echo number_format($users_data['total']); ?></span>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الإحصائيات</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users_data['users'] as $user): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="<?php echo $user['profile_image'] ?: 'assets/img/default-avatar.png'; ?>" 
                                                         alt="<?php echo htmlspecialchars($user['username']); ?>" 
                                                         class="rounded-circle me-3" width="40" height="40">
                                                    <div>
                                                        <div class="fw-bold">
                                                            <?php echo htmlspecialchars($user['full_name']); ?>
                                                            <?php if ($user['is_verified']): ?>
                                                                <i class="fas fa-check-circle text-primary ms-1" title="موثق"></i>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="text-muted small">@<?php echo htmlspecialchars($user['username']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                                            <td>
                                                <small class="text-muted">
                                                    <i class="fas fa-users"></i> <?php echo formatNumber($user['followers_count']); ?> |
                                                    <i class="fas fa-heart"></i> <?php echo formatNumber($user['likes_count']); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php if ($user['is_active']): ?>
                                                    <span class="badge bg-success">مفعل</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">غير مفعل</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo date('Y-m-d', strtotime($user['created_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <button type="button" class="btn btn-outline-primary" 
                                                            onclick="viewUser(<?php echo $user['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    
                                                    <?php if ($user['is_active']): ?>
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                            <input type="hidden" name="action" value="deactivate">
                                                            <button type="submit" class="btn btn-outline-warning" 
                                                                    onclick="return confirm('هل أنت متأكد من إلغاء تفعيل هذا المستخدم؟')">
                                                                <i class="fas fa-user-slash"></i>
                                                            </button>
                                                        </form>
                                                    <?php else: ?>
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                            <input type="hidden" name="action" value="activate">
                                                            <button type="submit" class="btn btn-outline-success">
                                                                <i class="fas fa-user-check"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                    
                                                    <?php if (!$user['is_verified']): ?>
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                            <input type="hidden" name="action" value="verify">
                                                            <button type="submit" class="btn btn-outline-info" title="توثيق">
                                                                <i class="fas fa-check-circle"></i>
                                                            </button>
                                                        </form>
                                                    <?php else: ?>
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                            <input type="hidden" name="action" value="unverify">
                                                            <button type="submit" class="btn btn-outline-secondary" title="إلغاء التوثيق">
                                                                <i class="fas fa-times-circle"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if ($users_data['pages'] > 1): ?>
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($users_data['current_page'] > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $users_data['current_page'] - 1; ?>&search=<?php echo urlencode($search); ?>">السابق</a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = 1; $i <= $users_data['pages']; $i++): ?>
                                <li class="page-item <?php echo $i == $users_data['current_page'] ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($users_data['current_page'] < $users_data['pages']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $users_data['current_page'] + 1; ?>&search=<?php echo urlencode($search); ?>">التالي</a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function filterUsers(status) {
            const url = new URL(window.location);
            url.searchParams.set('status', status);
            url.searchParams.set('page', '1');
            window.location = url;
        }
        
        function viewUser(userId) {
            // TODO: Implement user details modal
            alert('عرض تفاصيل المستخدم: ' + userId);
        }
    </script>
</body>
</html>
