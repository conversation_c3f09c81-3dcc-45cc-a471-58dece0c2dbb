# إعداد لوحة التحكم - Streamy Admin Panel

## متطلبات النظام:

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- مكتبة PDO PHP
- مكتبة GD PHP (لمعالجة الصور)

## خطوات التثبيت:

### 1. إعد<PERSON> قاعدة البيانات:

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE streamy_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم قاعدة البيانات (اختياري)
CREATE USER 'streamy_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON streamy_db.* TO 'streamy_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. تحديث إعدادات قاعدة البيانات:

عدّل ملف `admin_panel/config/database.php`:

```php
<?php
$host = 'localhost';
$dbname = 'streamy_db';
$username = 'streamy_user'; // أو 'root'
$password = 'your_password_here';
?>
```

### 3. رفع الملفات:

```bash
# نسخ مجلد admin_panel إلى خادم الويب
cp -r admin_panel/ /var/www/html/streamy_admin/

# أو باستخدام FTP/SFTP
# رفع جميع ملفات admin_panel إلى المجلد المطلوب
```

### 4. ضبط الصلاحيات:

```bash
# صلاحيات المجلدات
chmod 755 /var/www/html/streamy_admin/
chmod 755 /var/www/html/streamy_admin/assets/
chmod 755 /var/www/html/streamy_admin/uploads/

# صلاحيات الملفات
chmod 644 /var/www/html/streamy_admin/*.php
chmod 644 /var/www/html/streamy_admin/config/*.php

# مجلد الرفع (إذا كان موجوداً)
mkdir /var/www/html/streamy_admin/uploads/
chmod 777 /var/www/html/streamy_admin/uploads/
```

### 5. إعداد Apache Virtual Host (اختياري):

```apache
<VirtualHost *:80>
    ServerName streamy-admin.local
    DocumentRoot /var/www/html/streamy_admin
    
    <Directory /var/www/html/streamy_admin>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/streamy_admin_error.log
    CustomLog ${APACHE_LOG_DIR}/streamy_admin_access.log combined
</VirtualHost>
```

### 6. إعداد Nginx (اختياري):

```nginx
server {
    listen 80;
    server_name streamy-admin.local;
    root /var/www/html/streamy_admin;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
```

### 7. اختبار التثبيت:

1. افتح المتصفح واذهب إلى: `http://localhost/streamy_admin/`
2. يجب أن تظهر صفحة تسجيل الدخول
3. استخدم البيانات الافتراضية:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

### 8. إعدادات الأمان:

#### تغيير كلمة مرور المدير:
```sql
UPDATE admin_users 
SET password_hash = '$2y$10$newHashedPasswordHere' 
WHERE username = 'admin';
```

#### إنشاء ملف .htaccess للحماية:
```apache
# في مجلد config/
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

# في المجلد الرئيسي
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "*.env">
    Order Deny,Allow
    Deny from all
</Files>

# إعادة توجيه HTTPS (للإنتاج)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

### 9. إعداد النسخ الاحتياطي:

#### سكريبت النسخ الاحتياطي:
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/streamy"
DB_NAME="streamy_db"
DB_USER="streamy_user"
DB_PASS="your_password"

# إنشاء مجلد النسخ الاحتياطي
mkdir -p $BACKUP_DIR

# نسخ احتياطي لقاعدة البيانات
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# ضغط النسخة الاحتياطية
gzip $BACKUP_DIR/db_backup_$DATE.sql

# حذف النسخ القديمة (أكثر من 30 يوم)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

echo "Backup completed: $BACKUP_DIR/db_backup_$DATE.sql.gz"
```

#### إضافة إلى Cron:
```bash
# تشغيل النسخ الاحتياطي يومياً في الساعة 2:00 صباحاً
0 2 * * * /path/to/backup.sh
```

### 10. مراقبة الأداء:

#### تفعيل سجلات PHP:
```php
// في بداية config/database.php
ini_set('log_errors', 1);
ini_set('error_log', '/var/log/php/streamy_admin_errors.log');
```

#### مراقبة استخدام قاعدة البيانات:
```sql
-- عرض الاستعلامات البطيئة
SHOW VARIABLES LIKE 'slow_query_log';
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- عرض حالة قاعدة البيانات
SHOW STATUS LIKE 'Connections';
SHOW STATUS LIKE 'Threads_connected';
```

### 11. استكشاف الأخطاء:

#### مشاكل شائعة:

1. **خطأ اتصال قاعدة البيانات**:
   - تحقق من بيانات الاتصال في `config/database.php`
   - تأكد من تشغيل خدمة MySQL
   - تحقق من صلاحيات المستخدم

2. **صفحة بيضاء**:
   - تحقق من سجلات PHP
   - تأكد من تفعيل عرض الأخطاء للتطوير
   - تحقق من صلاحيات الملفات

3. **خطأ 500**:
   - راجع سجلات Apache/Nginx
   - تحقق من ملف .htaccess
   - تأكد من إصدار PHP المطلوب

4. **مشاكل الرفع**:
   - تحقق من صلاحيات مجلد uploads
   - راجع إعدادات PHP (upload_max_filesize, post_max_size)
   - تأكد من وجود مساحة كافية على القرص

#### أوامر التشخيص:
```bash
# فحص حالة Apache
sudo systemctl status apache2

# فحص حالة MySQL
sudo systemctl status mysql

# فحص سجلات الأخطاء
tail -f /var/log/apache2/error.log
tail -f /var/log/mysql/error.log

# فحص إعدادات PHP
php -m | grep pdo
php -m | grep gd
php --ini
```

### 12. تحسين الأداء:

#### إعدادات PHP:
```ini
; في php.ini
memory_limit = 256M
max_execution_time = 300
upload_max_filesize = 100M
post_max_size = 100M
max_file_uploads = 20

; تفعيل OPcache
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=4000
```

#### تحسين MySQL:
```sql
-- إضافة مؤشرات للأداء
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_videos_approved ON videos(is_approved);
CREATE INDEX idx_videos_created ON videos(created_at);
CREATE INDEX idx_reports_status ON reports(status);
```

### 13. الصيانة الدورية:

```sql
-- تنظيف الجلسات المنتهية الصلاحية
DELETE FROM sessions WHERE expires_at < NOW();

-- تحديث الإحصائيات
UPDATE users SET 
  followers_count = (SELECT COUNT(*) FROM follows WHERE following_id = users.id),
  following_count = (SELECT COUNT(*) FROM follows WHERE follower_id = users.id);

-- تحسين الجداول
OPTIMIZE TABLE users, videos, comments, likes, follows, reports;
```
