# إعداد Firebase للمشروع

## الخطوات المطلوبة:

### 1. إنشاء مشروع Firebase:
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إنشاء مشروع" أو "Create a project"
3. أدخل اسم المشروع: `Streamy`
4. اختر إعدادات Google Analytics (اختياري)
5. انقر على "إنشاء المشروع"

### 2. إضافة تطبيق Android:
1. في لوحة تحكم Firebase، انقر على أيقونة Android
2. أدخل اسم الحزمة: `com.example.streamy`
3. أدخل اسم التطبيق: `Streamy`
4. تحميل ملف `google-services.json`
5. ضع الملف في مجلد `android/app/`

### 3. تفعيل الخدمات المطلوبة:

#### Authentication:
1. اذهب إلى Authentication > Sign-in method
2. فعّل Email/Password
3. (اختياري) فعّل Google Sign-in

#### Firestore Database:
1. اذهب إلى Firestore Database
2. انقر على "إنشاء قاعدة بيانات"
3. اختر "Start in test mode" للتطوير
4. اختر الموقع الجغرافي

#### Storage:
1. اذهب إلى Storage
2. انقر على "البدء"
3. اختر "Start in test mode"

### 4. قواعد الأمان (Security Rules):

#### Firestore Rules:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null;
    }
    
    // Videos collection
    match /videos/{videoId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // Comments collection
    match /comments/{commentId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // Likes collection
    match /likes/{likeId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // Follows collection
    match /follows/{followId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == resource.data.followerId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.followerId;
    }
  }
}
```

#### Storage Rules:
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Videos
    match /videos/{userId}/{videoId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Profile images
    match /profile_images/{userId}/{imageId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Thumbnails
    match /thumbnails/{userId}/{thumbnailId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

### 5. إعداد المؤشرات (Indexes) في Firestore:

#### مؤشرات مطلوبة:
1. Collection: `videos`
   - Fields: `isPublic` (Ascending), `createdAt` (Descending)
   
2. Collection: `videos`
   - Fields: `userId` (Ascending), `createdAt` (Descending)
   
3. Collection: `comments`
   - Fields: `videoId` (Ascending), `createdAt` (Ascending)
   
4. Collection: `follows`
   - Fields: `followerId` (Ascending), `createdAt` (Descending)
   
5. Collection: `follows`
   - Fields: `followingId` (Ascending), `createdAt` (Descending)

### 6. إعداد Cloud Functions (اختياري):

```javascript
const functions = require('firebase-functions');
const admin = require('firebase-admin');
admin.initializeApp();

// Update user stats when video is created
exports.updateUserStatsOnVideoCreate = functions.firestore
  .document('videos/{videoId}')
  .onCreate(async (snap, context) => {
    const video = snap.data();
    const userRef = admin.firestore().collection('users').doc(video.userId);
    
    return userRef.update({
      videosCount: admin.firestore.FieldValue.increment(1)
    });
  });

// Update video stats when like is created
exports.updateVideoStatsOnLike = functions.firestore
  .document('likes/{likeId}')
  .onCreate(async (snap, context) => {
    const like = snap.data();
    const videoRef = admin.firestore().collection('videos').doc(like.videoId);
    
    return videoRef.update({
      likes: admin.firestore.FieldValue.increment(1)
    });
  });

// Update user stats when follow is created
exports.updateUserStatsOnFollow = functions.firestore
  .document('follows/{followId}')
  .onCreate(async (snap, context) => {
    const follow = snap.data();
    const batch = admin.firestore().batch();
    
    const followerRef = admin.firestore().collection('users').doc(follow.followerId);
    const followingRef = admin.firestore().collection('users').doc(follow.followingId);
    
    batch.update(followerRef, {
      following: admin.firestore.FieldValue.increment(1)
    });
    
    batch.update(followingRef, {
      followers: admin.firestore.FieldValue.increment(1)
    });
    
    return batch.commit();
  });
```

### 7. متغيرات البيئة:

إنشاء ملف `.env` في جذر المشروع:
```
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_API_KEY=your-api-key
FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
```

### 8. اختبار الإعداد:

1. تشغيل التطبيق: `flutter run`
2. تجربة تسجيل حساب جديد
3. تجربة تسجيل الدخول
4. تجربة رفع فيديو
5. التحقق من البيانات في Firebase Console

### 9. ملاحظات مهمة:

1. **الأمان**: غيّر قواعد الأمان قبل النشر للإنتاج
2. **الحصص**: راقب استخدام Firebase لتجنب تجاوز الحصص المجانية
3. **النسخ الاحتياطي**: فعّل النسخ الاحتياطي لـ Firestore
4. **المراقبة**: فعّل Firebase Performance Monitoring
5. **التحليلات**: فعّل Google Analytics للحصول على إحصائيات مفصلة

### 10. استكشاف الأخطاء:

#### مشاكل شائعة:
1. **ملف google-services.json مفقود**: تأكد من وضعه في `android/app/`
2. **خطأ في المصادقة**: تحقق من تفعيل Email/Password في Firebase
3. **خطأ في الصلاحيات**: راجع قواعد Firestore و Storage
4. **بطء في التحميل**: تحقق من سرعة الإنترنت وحجم الملفات

#### سجلات التشخيص:
```bash
# عرض سجلات Flutter
flutter logs

# عرض سجلات Firebase
firebase functions:log
```
