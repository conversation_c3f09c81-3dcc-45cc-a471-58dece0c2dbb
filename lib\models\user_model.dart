class UserModel {
  final String uid;
  final String email;
  final String username;
  final String fullName;
  final String profileImageUrl;
  final String bio;
  final int followers;
  final int following;
  final int likes;
  final DateTime createdAt;
  final List<String> followingList;
  final List<String> followersList;

  UserModel({
    required this.uid,
    required this.email,
    required this.username,
    required this.fullName,
    required this.profileImageUrl,
    required this.bio,
    required this.followers,
    required this.following,
    required this.likes,
    required this.createdAt,
    this.followingList = const [],
    this.followersList = const [],
  });

  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'email': email,
      'username': username,
      'fullName': fullName,
      'profileImageUrl': profileImageUrl,
      'bio': bio,
      'followers': followers,
      'following': following,
      'likes': likes,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'followingList': followingList,
      'followersList': followersList,
    };
  }

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      uid: map['uid'] ?? '',
      email: map['email'] ?? '',
      username: map['username'] ?? '',
      fullName: map['fullName'] ?? '',
      profileImageUrl: map['profileImageUrl'] ?? '',
      bio: map['bio'] ?? '',
      followers: map['followers']?.toInt() ?? 0,
      following: map['following']?.toInt() ?? 0,
      likes: map['likes']?.toInt() ?? 0,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      followingList: List<String>.from(map['followingList'] ?? []),
      followersList: List<String>.from(map['followersList'] ?? []),
    );
  }

  UserModel copyWith({
    String? uid,
    String? email,
    String? username,
    String? fullName,
    String? profileImageUrl,
    String? bio,
    int? followers,
    int? following,
    int? likes,
    DateTime? createdAt,
    List<String>? followingList,
    List<String>? followersList,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      username: username ?? this.username,
      fullName: fullName ?? this.fullName,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      bio: bio ?? this.bio,
      followers: followers ?? this.followers,
      following: following ?? this.following,
      likes: likes ?? this.likes,
      createdAt: createdAt ?? this.createdAt,
      followingList: followingList ?? this.followingList,
      followersList: followersList ?? this.followersList,
    );
  }

  @override
  String toString() {
    return 'UserModel(uid: $uid, email: $email, username: $username, fullName: $fullName, profileImageUrl: $profileImageUrl, bio: $bio, followers: $followers, following: $following, likes: $likes, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is UserModel &&
      other.uid == uid &&
      other.email == email &&
      other.username == username &&
      other.fullName == fullName &&
      other.profileImageUrl == profileImageUrl &&
      other.bio == bio &&
      other.followers == followers &&
      other.following == following &&
      other.likes == likes &&
      other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return uid.hashCode ^
      email.hashCode ^
      username.hashCode ^
      fullName.hashCode ^
      profileImageUrl.hashCode ^
      bio.hashCode ^
      followers.hashCode ^
      following.hashCode ^
      likes.hashCode ^
      createdAt.hashCode;
  }
}
