class VideoModel {
  final String id;
  final String userId;
  final String username;
  final String userProfileImage;
  final String videoUrl;
  final String thumbnailUrl;
  final String caption;
  final List<String> hashtags;
  final int likes;
  final int comments;
  final int shares;
  final int views;
  final DateTime createdAt;
  final List<String> likedBy;
  final bool isPublic;
  final String musicName;
  final String musicUrl;
  final double duration;

  VideoModel({
    required this.id,
    required this.userId,
    required this.username,
    required this.userProfileImage,
    required this.videoUrl,
    required this.thumbnailUrl,
    required this.caption,
    required this.hashtags,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.views,
    required this.createdAt,
    required this.likedBy,
    required this.isPublic,
    this.musicName = '',
    this.musicUrl = '',
    this.duration = 0.0,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'username': username,
      'userProfileImage': userProfileImage,
      'videoUrl': videoUrl,
      'thumbnailUrl': thumbnailUrl,
      'caption': caption,
      'hashtags': hashtags,
      'likes': likes,
      'comments': comments,
      'shares': shares,
      'views': views,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'likedBy': likedBy,
      'isPublic': isPublic,
      'musicName': musicName,
      'musicUrl': musicUrl,
      'duration': duration,
    };
  }

  factory VideoModel.fromMap(Map<String, dynamic> map) {
    return VideoModel(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      username: map['username'] ?? '',
      userProfileImage: map['userProfileImage'] ?? '',
      videoUrl: map['videoUrl'] ?? '',
      thumbnailUrl: map['thumbnailUrl'] ?? '',
      caption: map['caption'] ?? '',
      hashtags: List<String>.from(map['hashtags'] ?? []),
      likes: map['likes']?.toInt() ?? 0,
      comments: map['comments']?.toInt() ?? 0,
      shares: map['shares']?.toInt() ?? 0,
      views: map['views']?.toInt() ?? 0,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      likedBy: List<String>.from(map['likedBy'] ?? []),
      isPublic: map['isPublic'] ?? true,
      musicName: map['musicName'] ?? '',
      musicUrl: map['musicUrl'] ?? '',
      duration: map['duration']?.toDouble() ?? 0.0,
    );
  }

  VideoModel copyWith({
    String? id,
    String? userId,
    String? username,
    String? userProfileImage,
    String? videoUrl,
    String? thumbnailUrl,
    String? caption,
    List<String>? hashtags,
    int? likes,
    int? comments,
    int? shares,
    int? views,
    DateTime? createdAt,
    List<String>? likedBy,
    bool? isPublic,
    String? musicName,
    String? musicUrl,
    double? duration,
  }) {
    return VideoModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      username: username ?? this.username,
      userProfileImage: userProfileImage ?? this.userProfileImage,
      videoUrl: videoUrl ?? this.videoUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      caption: caption ?? this.caption,
      hashtags: hashtags ?? this.hashtags,
      likes: likes ?? this.likes,
      comments: comments ?? this.comments,
      shares: shares ?? this.shares,
      views: views ?? this.views,
      createdAt: createdAt ?? this.createdAt,
      likedBy: likedBy ?? this.likedBy,
      isPublic: isPublic ?? this.isPublic,
      musicName: musicName ?? this.musicName,
      musicUrl: musicUrl ?? this.musicUrl,
      duration: duration ?? this.duration,
    );
  }

  @override
  String toString() {
    return 'VideoModel(id: $id, userId: $userId, username: $username, caption: $caption, likes: $likes, comments: $comments, views: $views, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is VideoModel &&
      other.id == id &&
      other.userId == userId &&
      other.username == username &&
      other.userProfileImage == userProfileImage &&
      other.videoUrl == videoUrl &&
      other.thumbnailUrl == thumbnailUrl &&
      other.caption == caption &&
      other.likes == likes &&
      other.comments == comments &&
      other.shares == shares &&
      other.views == views &&
      other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      userId.hashCode ^
      username.hashCode ^
      userProfileImage.hashCode ^
      videoUrl.hashCode ^
      thumbnailUrl.hashCode ^
      caption.hashCode ^
      likes.hashCode ^
      comments.hashCode ^
      shares.hashCode ^
      views.hashCode ^
      createdAt.hashCode;
  }
}
