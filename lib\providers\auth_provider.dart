import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';

class AuthProvider extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  User? _user;
  UserModel? _userModel;
  bool _isLoading = false;
  String? _errorMessage;

  User? get user => _user;
  UserModel? get userModel => _userModel;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _user != null;

  AuthProvider() {
    _auth.authStateChanges().listen((User? user) {
      _user = user;
      if (user != null) {
        _loadUserData();
      } else {
        _userModel = null;
      }
      notifyListeners();
    });
  }

  Future<void> _loadUserData() async {
    if (_user != null) {
      try {
        DocumentSnapshot doc = await _firestore
            .collection('users')
            .doc(_user!.uid)
            .get();
        
        if (doc.exists) {
          _userModel = UserModel.fromMap(doc.data() as Map<String, dynamic>);
        }
      } catch (e) {
        _errorMessage = 'خطأ في تحميل بيانات المستخدم: $e';
      }
      notifyListeners();
    }
  }

  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      _user = result.user;
      await _loadUserData();
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.toString());
      notifyListeners();
      return false;
    }
  }

  Future<bool> signUpWithEmailAndPassword(
    String email, 
    String password, 
    String username,
    String fullName
  ) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Check if username is already taken
      QuerySnapshot usernameCheck = await _firestore
          .collection('users')
          .where('username', isEqualTo: username)
          .get();
      
      if (usernameCheck.docs.isNotEmpty) {
        _errorMessage = 'اسم المستخدم مستخدم بالفعل';
        _isLoading = false;
        notifyListeners();
        return false;
      }

      UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      _user = result.user;

      // Create user document in Firestore
      if (_user != null) {
        UserModel newUser = UserModel(
          uid: _user!.uid,
          email: email,
          username: username,
          fullName: fullName,
          profileImageUrl: '',
          bio: '',
          followers: 0,
          following: 0,
          likes: 0,
          createdAt: DateTime.now(),
        );

        await _firestore
            .collection('users')
            .doc(_user!.uid)
            .set(newUser.toMap());

        _userModel = newUser;
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.toString());
      notifyListeners();
      return false;
    }
  }

  Future<void> signOut() async {
    await _auth.signOut();
    _user = null;
    _userModel = null;
    notifyListeners();
  }

  Future<bool> updateProfile({
    String? fullName,
    String? bio,
    String? profileImageUrl,
  }) async {
    if (_user == null || _userModel == null) return false;

    try {
      _isLoading = true;
      notifyListeners();

      Map<String, dynamic> updates = {};
      if (fullName != null) updates['fullName'] = fullName;
      if (bio != null) updates['bio'] = bio;
      if (profileImageUrl != null) updates['profileImageUrl'] = profileImageUrl;

      await _firestore
          .collection('users')
          .doc(_user!.uid)
          .update(updates);

      // Update local user model
      _userModel = _userModel!.copyWith(
        fullName: fullName ?? _userModel!.fullName,
        bio: bio ?? _userModel!.bio,
        profileImageUrl: profileImageUrl ?? _userModel!.profileImageUrl,
      );

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'خطأ في تحديث الملف الشخصي: $e';
      notifyListeners();
      return false;
    }
  }

  String _getErrorMessage(String error) {
    if (error.contains('user-not-found')) {
      return 'المستخدم غير موجود';
    } else if (error.contains('wrong-password')) {
      return 'كلمة المرور غير صحيحة';
    } else if (error.contains('email-already-in-use')) {
      return 'البريد الإلكتروني مستخدم بالفعل';
    } else if (error.contains('weak-password')) {
      return 'كلمة المرور ضعيفة';
    } else if (error.contains('invalid-email')) {
      return 'البريد الإلكتروني غير صحيح';
    } else {
      return 'حدث خطأ غير متوقع';
    }
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
