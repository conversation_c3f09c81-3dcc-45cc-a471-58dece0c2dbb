import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:io';
import '../models/video_model.dart';

class VideoProvider extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  List<VideoModel> _videos = [];
  List<VideoModel> _userVideos = [];
  bool _isLoading = false;
  bool _isUploading = false;
  String? _errorMessage;
  DocumentSnapshot? _lastDocument;
  bool _hasMoreVideos = true;

  List<VideoModel> get videos => _videos;
  List<VideoModel> get userVideos => _userVideos;
  bool get isLoading => _isLoading;
  bool get isUploading => _isUploading;
  String? get errorMessage => _errorMessage;
  bool get hasMoreVideos => _hasMoreVideos;

  // Load videos for home feed
  Future<void> loadVideos({bool refresh = false}) async {
    if (_isLoading) return;

    try {
      _isLoading = true;
      if (refresh) {
        _videos.clear();
        _lastDocument = null;
        _hasMoreVideos = true;
      }
      notifyListeners();

      Query query = _firestore
          .collection('videos')
          .where('isPublic', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(10);

      if (_lastDocument != null) {
        query = query.startAfterDocument(_lastDocument!);
      }

      QuerySnapshot snapshot = await query.get();

      if (snapshot.docs.isEmpty) {
        _hasMoreVideos = false;
      } else {
        _lastDocument = snapshot.docs.last;
        List<VideoModel> newVideos = snapshot.docs
            .map(
              (doc) => VideoModel.fromMap(doc.data() as Map<String, dynamic>),
            )
            .toList();
        _videos.addAll(newVideos);
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'خطأ في تحميل الفيديوهات: $e';
      notifyListeners();
    }
  }

  // Load user's videos
  Future<void> loadUserVideos(String userId) async {
    try {
      _isLoading = true;
      notifyListeners();

      QuerySnapshot snapshot = await _firestore
          .collection('videos')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      _userVideos = snapshot.docs
          .map((doc) => VideoModel.fromMap(doc.data() as Map<String, dynamic>))
          .toList();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'خطأ في تحميل فيديوهات المستخدم: $e';
      notifyListeners();
    }
  }

  // Upload video
  Future<bool> uploadVideo({
    required File videoFile,
    required String caption,
    required List<String> hashtags,
    String musicName = '',
    String musicUrl = '',
  }) async {
    if (_auth.currentUser == null) {
      _errorMessage = 'يجب تسجيل الدخول أولاً';
      return false;
    }

    try {
      _isUploading = true;
      _errorMessage = null;
      notifyListeners();

      String videoId = DateTime.now().millisecondsSinceEpoch.toString();
      String userId = _auth.currentUser!.uid;

      // Check if file exists and is valid
      if (!await videoFile.exists()) {
        throw Exception('الملف غير موجود');
      }

      // Create a unique filename
      String fileName = 'video_$videoId.mp4';

      // Upload video to Firebase Storage with simpler path structure
      Reference videoRef = _storage.ref().child('videos').child(fileName);

      // تحقق من إعدادات Firebase Storage
      debugPrint('Uploading to path: videos/$fileName');

      // Add metadata
      SettableMetadata metadata = SettableMetadata(
        contentType: 'video/mp4',
        customMetadata: {
          'uploadedBy': userId,
          'uploadTime': DateTime.now().toIso8601String(),
          'caption': caption,
        },
      );

      UploadTask uploadTask = videoRef.putFile(videoFile, metadata);

      // Monitor upload progress
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        double progress = snapshot.bytesTransferred / snapshot.totalBytes;
        // يمكن إضافة callback هنا لعرض التقدم في الواجهة
        debugPrint('Upload progress: ${(progress * 100).toStringAsFixed(2)}%');
      });

      TaskSnapshot snapshot = await uploadTask;
      String videoUrl = await snapshot.ref.getDownloadURL();

      // Get user data
      DocumentSnapshot userDoc = await _firestore
          .collection('users')
          .doc(userId)
          .get();

      Map<String, dynamic> userData = userDoc.data() as Map<String, dynamic>;

      // Create video document
      VideoModel video = VideoModel(
        id: videoId,
        userId: userId,
        username: userData['username'] ?? '',
        userProfileImage: userData['profileImageUrl'] ?? '',
        videoUrl: videoUrl,
        thumbnailUrl: '', // Will be generated later
        caption: caption,
        hashtags: hashtags,
        likes: 0,
        comments: 0,
        shares: 0,
        views: 0,
        createdAt: DateTime.now(),
        likedBy: [],
        isPublic: true,
        musicName: musicName,
        musicUrl: musicUrl,
        duration: 0.0, // Will be calculated later
      );

      await _firestore.collection('videos').doc(videoId).set(video.toMap());

      _isUploading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isUploading = false;

      // معالجة أنواع مختلفة من الأخطاء
      if (e.toString().contains('object-not-found')) {
        _errorMessage =
            'خطأ في إعدادات Firebase Storage. تأكد من تفعيل Storage في Firebase Console';
      } else if (e.toString().contains('unauthorized')) {
        _errorMessage =
            'خطأ في الصلاحيات. تأكد من تسجيل الدخول وإعدادات Firebase';
      } else if (e.toString().contains('network')) {
        _errorMessage = 'خطأ في الاتصال بالإنترنت. تحقق من اتصالك';
      } else {
        _errorMessage = 'خطأ في رفع الفيديو: ${e.toString()}';
      }

      debugPrint('Video upload error: $e');
      notifyListeners();
      return false;
    }
  }

  // Like/Unlike video
  Future<void> toggleLike(String videoId) async {
    if (_auth.currentUser == null) return;

    try {
      String userId = _auth.currentUser!.uid;
      DocumentReference videoRef = _firestore.collection('videos').doc(videoId);

      await _firestore.runTransaction((transaction) async {
        DocumentSnapshot snapshot = await transaction.get(videoRef);

        if (!snapshot.exists) return;

        Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
        List<String> likedBy = List<String>.from(data['likedBy'] ?? []);
        int likes = data['likes'] ?? 0;

        if (likedBy.contains(userId)) {
          // Unlike
          likedBy.remove(userId);
          likes--;
        } else {
          // Like
          likedBy.add(userId);
          likes++;
        }

        transaction.update(videoRef, {'likedBy': likedBy, 'likes': likes});
      });

      // Update local data
      int videoIndex = _videos.indexWhere((video) => video.id == videoId);
      if (videoIndex != -1) {
        VideoModel video = _videos[videoIndex];
        List<String> likedBy = List.from(video.likedBy);
        int likes = video.likes;

        if (likedBy.contains(userId)) {
          likedBy.remove(userId);
          likes--;
        } else {
          likedBy.add(userId);
          likes++;
        }

        _videos[videoIndex] = video.copyWith(likedBy: likedBy, likes: likes);
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'خطأ في الإعجاب: $e';
      notifyListeners();
    }
  }

  // Increment view count
  Future<void> incrementViews(String videoId) async {
    try {
      await _firestore.collection('videos').doc(videoId).update({
        'views': FieldValue.increment(1),
      });

      // Update local data
      int videoIndex = _videos.indexWhere((video) => video.id == videoId);
      if (videoIndex != -1) {
        _videos[videoIndex] = _videos[videoIndex].copyWith(
          views: _videos[videoIndex].views + 1,
        );
        notifyListeners();
      }
    } catch (e) {
      // Silently fail for view count
    }
  }

  // Search videos
  Future<List<VideoModel>> searchVideos(String query) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('videos')
          .where('isPublic', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      List<VideoModel> allVideos = snapshot.docs
          .map((doc) => VideoModel.fromMap(doc.data() as Map<String, dynamic>))
          .toList();

      // Filter videos based on caption, hashtags, or username
      return allVideos.where((video) {
        return video.caption.toLowerCase().contains(query.toLowerCase()) ||
            video.hashtags.any(
              (tag) => tag.toLowerCase().contains(query.toLowerCase()),
            ) ||
            video.username.toLowerCase().contains(query.toLowerCase());
      }).toList();
    } catch (e) {
      _errorMessage = 'خطأ في البحث: $e';
      notifyListeners();
      return [];
    }
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
