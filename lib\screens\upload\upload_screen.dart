import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../providers/video_provider.dart';
import '../../utils/app_colors.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';

class UploadScreen extends StatefulWidget {
  const UploadScreen({super.key});

  @override
  State<UploadScreen> createState() => _UploadScreenState();
}

class _UploadScreenState extends State<UploadScreen> {
  final TextEditingController _captionController = TextEditingController();
  final TextEditingController _hashtagsController = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  File? _selectedVideo;
  bool _isPrivate = false;

  @override
  void dispose() {
    _captionController.dispose();
    _hashtagsController.dispose();
    super.dispose();
  }

  Future<void> _pickVideo() async {
    try {
      final XFile? video = await _picker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(minutes: 3), // Max 3 minutes
      );

      if (video != null) {
        setState(() {
          _selectedVideo = File(video.path);
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في اختيار الفيديو: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _recordVideo() async {
    try {
      final XFile? video = await _picker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(minutes: 3), // Max 3 minutes
      );

      if (video != null) {
        setState(() {
          _selectedVideo = File(video.path);
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تسجيل الفيديو: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _uploadVideo() async {
    if (_selectedVideo == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار فيديو أولاً'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    if (_captionController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إضافة وصف للفيديو'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    final videoProvider = Provider.of<VideoProvider>(context, listen: false);
    
    // Parse hashtags
    List<String> hashtags = _hashtagsController.text
        .split(' ')
        .where((tag) => tag.trim().isNotEmpty)
        .map((tag) => tag.replaceAll('#', '').trim())
        .toList();

    bool success = await videoProvider.uploadVideo(
      videoFile: _selectedVideo!,
      caption: _captionController.text.trim(),
      hashtags: hashtags,
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم رفع الفيديو بنجاح!'),
          backgroundColor: AppColors.success,
        ),
      );
      
      // Clear form
      setState(() {
        _selectedVideo = null;
        _captionController.clear();
        _hashtagsController.clear();
        _isPrivate = false;
      });
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(videoProvider.errorMessage ?? 'فشل في رفع الفيديو'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              const Text(
                'رفع فيديو جديد',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 30),

              // Video selection
              if (_selectedVideo == null) ...[
                _buildVideoSelectionCard(),
              ] else ...[
                _buildSelectedVideoCard(),
                const SizedBox(height: 30),
                _buildVideoDetailsForm(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoSelectionCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.video_library_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 20),
          const Text(
            'اختر فيديو للمشاركة',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            'يمكنك اختيار فيديو من المعرض أو تسجيل فيديو جديد',
            style: TextStyle(
              color: Colors.grey[400],
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'من المعرض',
                  icon: Icons.photo_library,
                  onPressed: _pickVideo,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomOutlineButton(
                  text: 'تسجيل جديد',
                  icon: Icons.videocam,
                  onPressed: _recordVideo,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedVideoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.play_circle_filled,
                  color: AppColors.primary,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'فيديو محدد',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _selectedVideo!.path.split('/').last,
                      style: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 12,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    _selectedVideo = null;
                  });
                },
                icon: const Icon(
                  Icons.close,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVideoDetailsForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Caption
        const Text(
          'وصف الفيديو',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        CustomTextField(
          controller: _captionController,
          hintText: 'اكتب وصفاً لفيديوك...',
          maxLines: 3,
          maxLength: 500,
        ),
        const SizedBox(height: 20),

        // Hashtags
        const Text(
          'الهاشتاغات',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        CustomTextField(
          controller: _hashtagsController,
          hintText: '#ترند #كوميديا #مضحك',
          prefixIcon: Icons.tag,
        ),
        const SizedBox(height: 20),

        // Privacy setting
        Row(
          children: [
            Switch(
              value: _isPrivate,
              onChanged: (value) {
                setState(() {
                  _isPrivate = value;
                });
              },
              activeColor: AppColors.primary,
            ),
            const SizedBox(width: 12),
            const Text(
              'فيديو خاص',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
        const SizedBox(height: 30),

        // Upload button
        Consumer<VideoProvider>(
          builder: (context, videoProvider, child) {
            return CustomButton(
              text: 'نشر الفيديو',
              onPressed: videoProvider.isUploading ? null : _uploadVideo,
              isLoading: videoProvider.isUploading,
              icon: Icons.publish,
            );
          },
        ),
      ],
    );
  }
}
